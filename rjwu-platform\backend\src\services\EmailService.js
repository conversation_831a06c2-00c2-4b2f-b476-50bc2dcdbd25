const nodemailer = require('nodemailer');
const BaseService = require('./BaseService');
const config = require('../config/config');

/**
 * Email Service
 * Handles all email-related operations
 */
class EmailService extends BaseService {
  constructor() {
    super('EmailService');
    this.transporter = null;
    this.initializeTransporter();
  }

  /**
   * Initialize email transporter
   */
  async initializeTransporter() {
    try {
      this.transporter = nodemailer.createTransporter({
        host: config.EMAIL_HOST,
        port: config.EMAIL_PORT,
        secure: config.EMAIL_PORT === 465,
        auth: {
          user: config.EMAIL_USER,
          pass: config.EMAIL_PASS
        }
      });

      // Verify connection
      await this.transporter.verify();
      this.log('info', 'Email transporter initialized successfully');
    } catch (error) {
      this.handleError(error, 'Email transporter initialization');
    }
  }

  /**
   * Send email
   * @param {Object} emailData - Email data
   * @param {string} emailData.to - Recipient email
   * @param {string} emailData.subject - Email subject
   * @param {string} emailData.html - HTML content
   * @param {string} emailData.text - Plain text content
   * @param {Array} emailData.attachments - Email attachments
   */
  async sendEmail({ to, subject, html, text, attachments = [] }) {
    try {
      this.validateRequired({ to, subject }, ['to', 'subject']);

      if (!html && !text) {
        throw new Error('Either HTML or text content is required');
      }

      const mailOptions = {
        from: `"RJWU Platform" <${config.EMAIL_USER}>`,
        to,
        subject,
        html,
        text,
        attachments
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      this.log('info', 'Email sent successfully', {
        to,
        subject,
        messageId: result.messageId
      });

      return this.formatResponse(result, 'Email sent successfully');
    } catch (error) {
      this.handleError(error, 'Send email', { to, subject });
    }
  }

  /**
   * Send welcome email to new users
   * @param {Object} user - User object
   */
  async sendWelcomeEmail(user) {
    try {
      const subject = 'Welcome to RJWU Platform!';
      const html = this.generateWelcomeEmailHTML(user);
      const text = this.generateWelcomeEmailText(user);

      return await this.sendEmail({
        to: user.email,
        subject,
        html,
        text
      });
    } catch (error) {
      this.handleError(error, 'Send welcome email', { userId: user._id });
    }
  }

  /**
   * Send email verification email
   * @param {Object} user - User object
   * @param {string} verificationToken - Verification token
   */
  async sendEmailVerification(user, verificationToken) {
    try {
      const verificationUrl = `${config.FRONTEND_URL}/verify-email?token=${verificationToken}`;
      const subject = 'Verify Your Email - RJWU Platform';
      const html = this.generateEmailVerificationHTML(user, verificationUrl);
      const text = this.generateEmailVerificationText(user, verificationUrl);

      return await this.sendEmail({
        to: user.email,
        subject,
        html,
        text
      });
    } catch (error) {
      this.handleError(error, 'Send email verification', { userId: user._id });
    }
  }

  /**
   * Send password reset email
   * @param {Object} user - User object
   * @param {string} resetToken - Password reset token
   */
  async sendPasswordReset(user, resetToken) {
    try {
      const resetUrl = `${config.FRONTEND_URL}/reset-password?token=${resetToken}`;
      const subject = 'Password Reset - RJWU Platform';
      const html = this.generatePasswordResetHTML(user, resetUrl);
      const text = this.generatePasswordResetText(user, resetUrl);

      return await this.sendEmail({
        to: user.email,
        subject,
        html,
        text
      });
    } catch (error) {
      this.handleError(error, 'Send password reset', { userId: user._id });
    }
  }

  /**
   * Send course enrollment confirmation
   * @param {Object} user - User object
   * @param {Object} course - Course object
   */
  async sendCourseEnrollmentConfirmation(user, course) {
    try {
      const subject = `Course Enrollment Confirmed - ${course.title}`;
      const html = this.generateEnrollmentConfirmationHTML(user, course);
      const text = this.generateEnrollmentConfirmationText(user, course);

      return await this.sendEmail({
        to: user.email,
        subject,
        html,
        text
      });
    } catch (error) {
      this.handleError(error, 'Send enrollment confirmation', { 
        userId: user._id, 
        courseId: course._id 
      });
    }
  }

  /**
   * Generate welcome email HTML
   */
  generateWelcomeEmailHTML(user) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Welcome to RJWU Platform!</h1>
        <p>Dear ${user.firstName} ${user.lastName},</p>
        <p>Welcome to RJWU Platform, Rajasthan's premier EduTech platform. We're excited to have you join our learning community!</p>
        <p>You can now:</p>
        <ul>
          <li>Browse and enroll in courses</li>
          <li>Join live classes and batches</li>
          <li>Track your learning progress</li>
          <li>Connect with instructors and fellow students</li>
        </ul>
        <p>Get started by exploring our course catalog and finding the perfect course for you.</p>
        <a href="${config.FRONTEND_URL}/courses" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0;">Browse Courses</a>
        <p>If you have any questions, feel free to contact our support team.</p>
        <p>Happy learning!</p>
        <p>The RJWU Team</p>
      </div>
    `;
  }

  /**
   * Generate welcome email text
   */
  generateWelcomeEmailText(user) {
    return `
Welcome to RJWU Platform!

Dear ${user.firstName} ${user.lastName},

Welcome to RJWU Platform, Rajasthan's premier EduTech platform. We're excited to have you join our learning community!

You can now:
- Browse and enroll in courses
- Join live classes and batches
- Track your learning progress
- Connect with instructors and fellow students

Get started by exploring our course catalog at: ${config.FRONTEND_URL}/courses

If you have any questions, feel free to contact our support team.

Happy learning!
The RJWU Team
    `;
  }

  /**
   * Generate email verification HTML
   */
  generateEmailVerificationHTML(user, verificationUrl) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Verify Your Email</h1>
        <p>Dear ${user.firstName},</p>
        <p>Please verify your email address to complete your registration on RJWU Platform.</p>
        <a href="${verificationUrl}" style="background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0;">Verify Email</a>
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all;">${verificationUrl}</p>
        <p>This link will expire in 24 hours.</p>
        <p>If you didn't create an account with us, please ignore this email.</p>
        <p>The RJWU Team</p>
      </div>
    `;
  }

  /**
   * Generate email verification text
   */
  generateEmailVerificationText(user, verificationUrl) {
    return `
Verify Your Email

Dear ${user.firstName},

Please verify your email address to complete your registration on RJWU Platform.

Verification link: ${verificationUrl}

This link will expire in 24 hours.

If you didn't create an account with us, please ignore this email.

The RJWU Team
    `;
  }

  /**
   * Generate password reset HTML
   */
  generatePasswordResetHTML(user, resetUrl) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Password Reset</h1>
        <p>Dear ${user.firstName},</p>
        <p>You requested a password reset for your RJWU Platform account.</p>
        <a href="${resetUrl}" style="background-color: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0;">Reset Password</a>
        <p>If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="word-break: break-all;">${resetUrl}</p>
        <p>This link will expire in 1 hour.</p>
        <p>If you didn't request this reset, please ignore this email and your password will remain unchanged.</p>
        <p>The RJWU Team</p>
      </div>
    `;
  }

  /**
   * Generate password reset text
   */
  generatePasswordResetText(user, resetUrl) {
    return `
Password Reset

Dear ${user.firstName},

You requested a password reset for your RJWU Platform account.

Reset link: ${resetUrl}

This link will expire in 1 hour.

If you didn't request this reset, please ignore this email and your password will remain unchanged.

The RJWU Team
    `;
  }

  /**
   * Generate enrollment confirmation HTML
   */
  generateEnrollmentConfirmationHTML(user, course) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2563eb;">Course Enrollment Confirmed!</h1>
        <p>Dear ${user.firstName},</p>
        <p>Congratulations! You have successfully enrolled in:</p>
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h2 style="margin: 0; color: #1f2937;">${course.title}</h2>
          <p style="margin: 10px 0 0 0; color: #6b7280;">${course.description}</p>
        </div>
        <p>You can now access your course materials and start learning!</p>
        <a href="${config.FRONTEND_URL}/courses/${course._id}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0;">Start Learning</a>
        <p>Happy learning!</p>
        <p>The RJWU Team</p>
      </div>
    `;
  }

  /**
   * Generate enrollment confirmation text
   */
  generateEnrollmentConfirmationText(user, course) {
    return `
Course Enrollment Confirmed!

Dear ${user.firstName},

Congratulations! You have successfully enrolled in:

${course.title}
${course.description}

You can now access your course materials and start learning!

Course link: ${config.FRONTEND_URL}/courses/${course._id}

Happy learning!
The RJWU Team
    `;
  }
}

module.exports = new EmailService();
