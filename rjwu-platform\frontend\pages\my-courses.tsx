import React, { useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import DashboardLayout from '@/components/layouts/DashboardLayout'
import { 
  PlayIcon,
  ClockIcon,
  CheckCircleIcon,
  BookmarkIcon,
  CalendarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline'

// Mock data - in real app, this would come from API
const mockEnrolledCourses = [
  {
    id: 1,
    title: 'Complete React.js Development Course',
    instructor: 'Dr. <PERSON>',
    thumbnail: 'https://via.placeholder.com/300x200',
    progress: 75,
    totalLessons: 20,
    completedLessons: 15,
    totalDuration: '40 hours',
    timeSpent: '30 hours',
    lastAccessed: '2024-01-14',
    nextLesson: {
      id: 16,
      title: 'Advanced Hooks Patterns',
      duration: '25 min'
    },
    certificate: null,
    enrollmentDate: '2023-12-01',
    status: 'in_progress'
  },
  {
    id: 2,
    title: 'Node.js Backend Development Masterclass',
    instructor: 'Prof<PERSON> <PERSON>',
    thumbnail: 'https://via.placeholder.com/300x200',
    progress: 45,
    totalLessons: 25,
    completedLessons: 11,
    totalDuration: '35 hours',
    timeSpent: '16 hours',
    lastAccessed: '2024-01-12',
    nextLesson: {
      id: 12,
      title: 'Express.js Middleware Deep Dive',
      duration: '30 min'
    },
    certificate: null,
    enrollmentDate: '2023-12-15',
    status: 'in_progress'
  },
  {
    id: 3,
    title: 'JavaScript Fundamentals',
    instructor: 'Dr. Emily Rodriguez',
    thumbnail: 'https://via.placeholder.com/300x200',
    progress: 100,
    totalLessons: 15,
    completedLessons: 15,
    totalDuration: '20 hours',
    timeSpent: '22 hours',
    lastAccessed: '2024-01-10',
    nextLesson: null,
    certificate: {
      id: 'cert_001',
      issuedDate: '2024-01-10',
      downloadUrl: '/certificates/cert_001.pdf'
    },
    enrollmentDate: '2023-11-01',
    status: 'completed'
  }
]

const filterOptions = ['All', 'In Progress', 'Completed', 'Not Started']
const sortOptions = ['Recently Accessed', 'Progress', 'Enrollment Date', 'Course Title']

export default function MyCoursesPage() {
  const [selectedFilter, setSelectedFilter] = useState('All')
  const [sortBy, setSortBy] = useState('Recently Accessed')

  const filteredCourses = mockEnrolledCourses.filter(course => {
    if (selectedFilter === 'All') return true
    if (selectedFilter === 'In Progress') return course.status === 'in_progress'
    if (selectedFilter === 'Completed') return course.status === 'completed'
    if (selectedFilter === 'Not Started') return course.progress === 0
    return true
  })

  const getStatusBadge = (status: string, progress: number) => {
    if (status === 'completed') {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircleIcon className="h-3 w-3 mr-1" />
          Completed
        </span>
      )
    } else if (progress > 0) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          <PlayIcon className="h-3 w-3 mr-1" />
          In Progress
        </span>
      )
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          <BookmarkIcon className="h-3 w-3 mr-1" />
          Not Started
        </span>
      )
    }
  }

  return (
    <>
      <Head>
        <title>My Courses - RJWU Platform</title>
        <meta name="description" content="View and continue your enrolled courses" />
      </Head>

      <DashboardLayout userRole="student">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">My Courses</h1>
              <p className="mt-1 text-sm text-gray-600">
                Continue your learning journey with your enrolled courses
              </p>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <BookmarkIcon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Enrolled</p>
                  <p className="text-2xl font-semibold text-gray-900">{mockEnrolledCourses.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <PlayIcon className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">In Progress</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {mockEnrolledCourses.filter(c => c.status === 'in_progress').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {mockEnrolledCourses.filter(c => c.status === 'completed').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <ClockIcon className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Hours</p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {mockEnrolledCourses.reduce((total, course) => {
                      const hours = parseInt(course.timeSpent.split(' ')[0])
                      return total + hours
                    }, 0)}h
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Filters and Sort */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div className="flex flex-wrap gap-2">
                {filterOptions.map((filter) => (
                  <button
                    key={filter}
                    onClick={() => setSelectedFilter(filter)}
                    className={`px-4 py-2 rounded-md text-sm font-medium ${
                      selectedFilter === filter
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {filter}
                  </button>
                ))}
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">Sort by:</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  {sortOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Courses Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCourses.map((course) => (
              <div key={course.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative">
                  <img
                    src={course.thumbnail}
                    alt={course.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-2 left-2">
                    {getStatusBadge(course.status, course.progress)}
                  </div>
                  {course.certificate && (
                    <div className="absolute top-2 right-2">
                      <span className="bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded">
                        Certified
                      </span>
                    </div>
                  )}
                </div>

                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                    {course.title}
                  </h3>
                  
                  <p className="text-sm text-gray-600 mb-4">by {course.instructor}</p>

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                      <span>{course.completedLessons}/{course.totalLessons} lessons</span>
                      <span>{course.progress}% complete</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${course.progress}%` }}
                      />
                    </div>
                  </div>

                  {/* Course Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <div className="flex items-center">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      {course.timeSpent} / {course.totalDuration}
                    </div>
                    <div className="flex items-center">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      {course.lastAccessed}
                    </div>
                  </div>

                  {/* Next Lesson or Certificate */}
                  {course.nextLesson ? (
                    <div className="bg-gray-50 rounded-lg p-3 mb-4">
                      <p className="text-sm font-medium text-gray-900">Next Lesson:</p>
                      <p className="text-sm text-gray-600">{course.nextLesson.title}</p>
                      <p className="text-xs text-gray-500">{course.nextLesson.duration}</p>
                    </div>
                  ) : course.certificate ? (
                    <div className="bg-green-50 rounded-lg p-3 mb-4">
                      <p className="text-sm font-medium text-green-800">Course Completed!</p>
                      <p className="text-sm text-green-600">Certificate earned on {course.certificate.issuedDate}</p>
                    </div>
                  ) : null}

                  {/* Action Buttons */}
                  <div className="flex space-x-2">
                    {course.status === 'completed' ? (
                      <>
                        <Link
                          href={`/courses/${course.id}/review`}
                          className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-md text-sm font-medium text-center hover:bg-gray-200"
                        >
                          Review
                        </Link>
                        {course.certificate && (
                          <a
                            href={course.certificate.downloadUrl}
                            className="flex-1 bg-yellow-500 text-white py-2 px-4 rounded-md text-sm font-medium text-center hover:bg-yellow-600"
                            download
                          >
                            Certificate
                          </a>
                        )}
                      </>
                    ) : (
                      <>
                        <Link
                          href={`/courses/${course.id}/learn`}
                          className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md text-sm font-medium text-center hover:bg-blue-700"
                        >
                          {course.progress > 0 ? 'Continue' : 'Start'}
                        </Link>
                        <Link
                          href={`/courses/${course.id}`}
                          className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-md text-sm font-medium text-center hover:bg-gray-200"
                        >
                          Details
                        </Link>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* No Courses Message */}
          {filteredCourses.length === 0 && (
            <div className="text-center py-12">
              <BookmarkIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No courses found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {selectedFilter === 'All' 
                  ? "You haven't enrolled in any courses yet."
                  : `No courses match the "${selectedFilter}" filter.`
                }
              </p>
              {selectedFilter === 'All' && (
                <div className="mt-6">
                  <Link
                    href="/courses"
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Browse Courses
                  </Link>
                </div>
              )}
            </div>
          )}
        </div>
      </DashboardLayout>
    </>
  )
}
