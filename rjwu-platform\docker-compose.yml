version: '3.8'

services:
  mongodb:
    image: mongo:6.0
    container_name: rjwu-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: rjwu
    volumes:
      - mongodb_data:/data/db
    networks:
      - rjwu-network

  redis:
    image: redis:7-alpine
    container_name: rjwu-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - rjwu-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: rjwu-backend
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=development
      - PORT=5000
      - MONGODB_URI=************************************************************
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=rjwu-dev-jwt-secret-2024-change-in-production
      - EMAIL_HOST=smtp.ethereal.email
      - EMAIL_PORT=587
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASS=dev-password
      - CLOUDINARY_CLOUD_NAME=rjwu-dev-cloud
      - CLOUDINARY_API_KEY=123456789012345
      - CLOUDINARY_API_SECRET=dev-cloudinary-secret
      - FRONTEND_URL=http://localhost:3000
      - SESSION_SECRET=rjwu-dev-session-secret-2024
      - DEBUG=rjwu:*
      - LOG_LEVEL=debug
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
      - backend_uploads:/app/uploads
    networks:
      - rjwu-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:5000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: rjwu-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_APP_NAME=RJWU Platform
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - NEXT_PUBLIC_API_URL=http://localhost:5000/api
      - NEXT_PUBLIC_SOCKET_URL=http://localhost:5000
      - NEXT_PUBLIC_DEBUG=true
      - NEXT_PUBLIC_ENABLE_PWA=true
      - NEXT_PUBLIC_ENABLE_OFFLINE=false
      - NEXT_TELEMETRY_DISABLED=1
    depends_on:
      backend:
        condition: service_healthy
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - rjwu-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health", "||", "exit", "1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local

networks:
  rjwu-network:
    driver: bridge
