# Database Guide

## Overview

This guide provides comprehensive information about database management, migrations, and seeding for the RJWU EduTech Platform. The platform uses MongoDB as the primary database with Mongoose ODM for data modeling.

## Database Structure

### Collections

1. **Users** - Student, instructor, and admin accounts
2. **Courses** - Course content and metadata
3. **Batches** - Live class sessions and student groups
4. **Payments** - Payment transactions and records
5. **Migrations** - Database migration tracking

### Indexes

The platform uses strategic indexing for optimal query performance:

- **Users**: email (unique), phone (unique), role, isActive, enrolledCourses.course
- **Courses**: title, instructor, category, level, status, text search
- **Batches**: course, instructor, status, dates, students
- **Payments**: user, course, status, gateway, dates

## Database Setup

### Initial Setup

```bash
# Run complete database setup (migrations + seeding)
npm run db:setup

# Or run individually
npm run db:migrate      # Run migrations
npm run db:seed         # Seed sample data
```

### Environment Configuration

Ensure your `.env` file contains the correct database configuration:

```env
MONGODB_URI=mongodb://localhost:27017/rjwu_dev
# or for Docker
MONGODB_URI=************************************************************
```

## Migrations

### Migration System

The platform uses a custom migration system to manage database schema changes and data transformations.

### Running Migrations

```bash
# Run all pending migrations
npm run db:migrate

# Check migration status
npm run db:migrate:status

# Rollback specific migration
npm run db:migrate:down <migration_name>

# Create new migration
npm run db:migrate:create <name> "<description>"
```

### Migration Structure

```javascript
// Example migration file
const mongoose = require('mongoose');
const logger = require('../../src/utils/logger');

async function up() {
  // Migration logic here
  logger.info('Running migration: example_migration');
  
  const db = mongoose.connection.db;
  await db.collection('users').createIndex({ email: 1 }, { unique: true });
  
  logger.info('Migration completed successfully');
}

async function down() {
  // Rollback logic here
  logger.info('Rolling back migration: example_migration');
  
  const db = mongoose.connection.db;
  await db.collection('users').dropIndex('email_1');
  
  logger.info('Migration rollback completed');
}

module.exports = {
  up,
  down,
  description: 'Add email index to users collection'
};
```

### Migration Best Practices

1. **Always test migrations** on development data first
2. **Write rollback logic** for every migration
3. **Use descriptive names** and descriptions
4. **Keep migrations atomic** - one logical change per migration
5. **Backup production data** before running migrations

## Data Seeding

### Sample Data

The seeding system creates realistic sample data for development and testing:

- **Admin User**: <EMAIL> / admin123
- **Instructors**: 3 sample instructors with different specializations
- **Students**: 3 sample students with various backgrounds
- **Courses**: 4+ comprehensive courses with detailed content
- **Batches**: Active learning batches with schedules
- **Payments**: Sample payment records

### Seeding Process

```bash
# Full database reset and seeding
npm run db:reset

# Just run seeding (preserves existing data)
npm run db:seed
```

### Custom Seeders

Create custom seeders for specific data types:

```javascript
// Example: CourseSeeder.js
class CourseSeeder {
  async seed() {
    const instructors = await User.find({ role: 'instructor' });
    const courseData = this.generateCourseData(instructors);
    return await Course.insertMany(courseData);
  }
  
  generateCourseData(instructors) {
    return [
      {
        title: 'Sample Course',
        instructor: instructors[0]._id,
        // ... other course data
      }
    ];
  }
}
```

## Database Operations

### Common Operations

```bash
# Connect to MongoDB (if running locally)
mongosh mongodb://localhost:27017/rjwu_dev

# Connect to Docker MongoDB
mongosh **************************************************************

# Basic queries
db.users.find({ role: 'student' })
db.courses.find({ status: 'published' })
db.payments.find({ status: 'completed' })
```

### Backup and Restore

```bash
# Create backup
mongodump --uri="mongodb://localhost:27017/rjwu_dev" --out=./backup

# Restore backup
mongorestore --uri="mongodb://localhost:27017/rjwu_dev" ./backup/rjwu_dev

# Docker backup
docker exec rjwu-mongodb mongodump --out /backup
docker cp rjwu-mongodb:/backup ./mongodb-backup
```

## Development Workflow

### Setting Up Development Database

1. **Start MongoDB** (locally or via Docker)
2. **Run migrations** to set up schema
3. **Seed sample data** for development
4. **Verify setup** by checking collections

```bash
# Complete setup
docker-compose up -d mongodb
npm run db:setup

# Verify
mongosh **************************************************************
> show collections
> db.users.countDocuments()
```

### Adding New Features

1. **Create migration** for schema changes
2. **Update models** if necessary
3. **Add seeder data** for new features
4. **Test thoroughly** with sample data

### Data Consistency

- **Use transactions** for multi-document operations
- **Validate data** at application level
- **Implement proper error handling**
- **Monitor data integrity** regularly

## Production Considerations

### Database Security

- **Enable authentication** on MongoDB
- **Use strong passwords** for database users
- **Limit network access** to database
- **Enable SSL/TLS** for connections
- **Regular security updates**

### Performance Optimization

- **Monitor query performance** with MongoDB Profiler
- **Optimize indexes** based on query patterns
- **Use aggregation pipelines** for complex queries
- **Implement connection pooling**
- **Consider read replicas** for scaling

### Backup Strategy

- **Automated daily backups**
- **Point-in-time recovery** capability
- **Test restore procedures** regularly
- **Store backups securely** off-site
- **Document recovery procedures**

### Monitoring

- **Database performance metrics**
- **Connection pool monitoring**
- **Disk space usage**
- **Query performance**
- **Error rate tracking**

## Troubleshooting

### Common Issues

1. **Connection Refused**
   ```bash
   # Check if MongoDB is running
   docker-compose ps mongodb
   
   # Check logs
   docker-compose logs mongodb
   ```

2. **Authentication Failed**
   ```bash
   # Verify credentials in .env
   # Check MongoDB user permissions
   ```

3. **Migration Failures**
   ```bash
   # Check migration status
   npm run db:migrate:status
   
   # Review migration logs
   # Fix issues and retry
   ```

4. **Seeding Errors**
   ```bash
   # Clear database and retry
   npm run db:reset
   
   # Check for data conflicts
   ```

### Performance Issues

1. **Slow Queries**
   - Enable MongoDB profiler
   - Analyze query execution plans
   - Add appropriate indexes

2. **High Memory Usage**
   - Monitor working set size
   - Optimize query patterns
   - Consider data archiving

3. **Connection Pool Exhaustion**
   - Increase pool size
   - Fix connection leaks
   - Implement proper error handling

## Schema Evolution

### Versioning Strategy

- **Semantic versioning** for major schema changes
- **Backward compatibility** when possible
- **Gradual migration** for large datasets
- **Feature flags** for new schema features

### Data Migration Patterns

1. **Additive Changes** - Add new fields with defaults
2. **Transformative Changes** - Migrate data in batches
3. **Destructive Changes** - Careful planning and backups
4. **Rollback Strategy** - Always plan for rollbacks

## Best Practices

### Development

- **Use meaningful collection names**
- **Follow consistent naming conventions**
- **Validate data at multiple levels**
- **Write comprehensive tests**
- **Document schema changes**

### Production

- **Monitor database health**
- **Implement proper logging**
- **Use connection pooling**
- **Plan for scalability**
- **Regular maintenance tasks**

### Security

- **Principle of least privilege**
- **Regular security audits**
- **Encrypt sensitive data**
- **Secure backup storage**
- **Monitor access patterns**

## Resources

- [MongoDB Documentation](https://docs.mongodb.com/)
- [Mongoose Documentation](https://mongoosejs.com/docs/)
- [MongoDB Best Practices](https://docs.mongodb.com/manual/administration/production-notes/)
- [Database Design Patterns](https://docs.mongodb.com/manual/applications/data-models/)
