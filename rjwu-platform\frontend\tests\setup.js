import '@testing-library/jest-dom'
import { TextEncoder, TextDecoder } from 'util'

// Polyfills for Node.js environment
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn(),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
      isFallback: false,
    }
  },
}))

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} />
  },
}))

// Mock Next.js Link component
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, href, ...props }) => {
    return <a href={href} {...props}>{children}</a>
  },
}))

// Mock environment variables
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:5000/api'
process.env.NEXT_PUBLIC_APP_NAME = 'RJWU Platform'
process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000'

// Mock fetch API
global.fetch = jest.fn()

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn(() => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
  unobserve: jest.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = jest.fn(() => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
  unobserve: jest.fn(),
}))

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.sessionStorage = sessionStorageMock

// Mock window.location
delete window.location
window.location = {
  href: 'http://localhost:3000',
  origin: 'http://localhost:3000',
  protocol: 'http:',
  host: 'localhost:3000',
  hostname: 'localhost',
  port: '3000',
  pathname: '/',
  search: '',
  hash: '',
  assign: jest.fn(),
  replace: jest.fn(),
  reload: jest.fn(),
}

// Global test utilities
global.testUtils = {
  // Create mock user data
  createMockUser: (overrides = {}) => ({
    _id: '507f1f77bcf86cd799439011',
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '9876543210',
    role: 'student',
    isActive: true,
    isEmailVerified: true,
    avatar: null,
    createdAt: new Date().toISOString(),
    ...overrides
  }),

  // Create mock course data
  createMockCourse: (overrides = {}) => ({
    _id: '507f1f77bcf86cd799439012',
    title: 'Test Course',
    description: 'This is a test course',
    category: 'Technology',
    level: 'beginner',
    price: 999,
    duration: 30,
    rating: 4.5,
    enrollmentCount: 100,
    thumbnail: 'https://example.com/thumbnail.jpg',
    instructor: {
      _id: '507f1f77bcf86cd799439013',
      firstName: 'Test',
      lastName: 'Instructor',
      email: '<EMAIL>'
    },
    createdAt: new Date().toISOString(),
    ...overrides
  }),

  // Create mock API response
  createMockApiResponse: (data, status = 'success') => ({
    status,
    data,
    message: status === 'success' ? 'Operation successful' : 'Operation failed',
    timestamp: new Date().toISOString()
  }),

  // Mock fetch response
  mockFetchResponse: (data, status = 200) => {
    global.fetch.mockResolvedValueOnce({
      ok: status >= 200 && status < 300,
      status,
      json: async () => data,
      text: async () => JSON.stringify(data),
    })
  },

  // Mock fetch error
  mockFetchError: (error = new Error('Network error')) => {
    global.fetch.mockRejectedValueOnce(error)
  },

  // Wait for async operations
  waitFor: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),

  // Create mock event
  createMockEvent: (overrides = {}) => ({
    preventDefault: jest.fn(),
    stopPropagation: jest.fn(),
    target: { value: '' },
    currentTarget: { value: '' },
    ...overrides
  })
}

// Setup and teardown
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks()
  
  // Reset fetch mock
  global.fetch.mockClear()
  
  // Clear localStorage and sessionStorage
  localStorageMock.clear()
  sessionStorageMock.clear()
})

afterEach(() => {
  // Clean up after each test
  jest.restoreAllMocks()
})
