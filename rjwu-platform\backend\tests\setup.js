const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

let mongoServer;

// Setup before all tests
beforeAll(async () => {
  // Start in-memory MongoDB instance
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();

  // Connect to the in-memory database
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.EMAIL_HOST = 'test.example.com';
  process.env.EMAIL_USER = '<EMAIL>';
  process.env.EMAIL_PASS = 'test-password';
  process.env.CLOUDINARY_CLOUD_NAME = 'test-cloud';
  process.env.CLOUDINARY_API_KEY = 'test-key';
  process.env.CLOUDINARY_API_SECRET = 'test-secret';
  process.env.RAZORPAY_KEY_ID = 'test-razorpay-key';
  process.env.RAZORPAY_KEY_SECRET = 'test-razorpay-secret';
});

// Cleanup after each test
afterEach(async () => {
  // Clear all collections
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// Cleanup after all tests
afterAll(async () => {
  // Close database connection
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  
  // Stop the in-memory MongoDB instance
  await mongoServer.stop();
});

// Global test utilities
global.testUtils = {
  // Create test user data
  createTestUser: (overrides = {}) => ({
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '9876543210',
    password: 'testpassword123',
    role: 'student',
    isEmailVerified: true,
    isPhoneVerified: true,
    ...overrides
  }),

  // Create test course data
  createTestCourse: (overrides = {}) => ({
    title: 'Test Course',
    description: 'This is a test course',
    category: 'Technology',
    level: 'beginner',
    price: 999,
    duration: 30,
    language: 'english',
    instructor: null, // Will be set in tests
    ...overrides
  }),

  // Create test batch data
  createTestBatch: (overrides = {}) => ({
    name: 'Test Batch',
    description: 'This is a test batch',
    course: null, // Will be set in tests
    instructor: null, // Will be set in tests
    startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    endDate: new Date(Date.now() + 37 * 24 * 60 * 60 * 1000), // 37 days from now
    maxStudents: 50,
    schedule: {
      days: ['monday', 'wednesday', 'friday'],
      startTime: '10:00',
      endTime: '11:30'
    },
    ...overrides
  }),

  // Generate JWT token for testing
  generateTestToken: (userId) => {
    const jwt = require('jsonwebtoken');
    return jwt.sign({ id: userId }, process.env.JWT_SECRET, { expiresIn: '1h' });
  },

  // Wait for a specified time
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms))
};
