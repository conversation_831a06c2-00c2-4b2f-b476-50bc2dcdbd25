const Course = require('../../src/models/Course');
const User = require('../../src/models/User');
const logger = require('../../src/utils/logger');

/**
 * Course Seeder
 * Creates sample courses with detailed content
 */

class CourseSeeder {
  constructor() {
    this.courses = [];
  }

  async getInstructors() {
    try {
      return await User.find({ role: 'instructor' });
    } catch (error) {
      logger.error('Error fetching instructors:', error);
      return [];
    }
  }

  generateCourseData(instructors) {
    return [
      {
        title: 'Full Stack Web Development Bootcamp',
        description: 'Complete full stack development course covering frontend, backend, and deployment.',
        longDescription: `This comprehensive bootcamp will take you from a complete beginner to a full-stack web developer. 
        You'll learn HTML, CSS, JavaScript, React, Node.js, Express, MongoDB, and deployment strategies. 
        The course includes hands-on projects, real-world applications, and industry best practices.`,
        instructor: instructors[0]?._id,
        category: 'Full Stack Development',
        level: 'beginner',
        price: 4999,
        originalPrice: 7999,
        duration: 60,
        language: 'english',
        thumbnail: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800',
        status: 'published',
        isActive: true,
        settings: {
          isPublished: true,
          allowComments: true,
          allowDownloads: true,
          prerequisites: ['Basic computer knowledge', 'Willingness to learn'],
          learningOutcomes: [
            'Build complete web applications from scratch',
            'Master HTML, CSS, and JavaScript',
            'Create responsive and interactive user interfaces',
            'Develop RESTful APIs with Node.js and Express',
            'Work with databases using MongoDB',
            'Deploy applications to cloud platforms',
            'Implement user authentication and authorization',
            'Follow industry best practices and coding standards'
          ]
        },
        chapters: [
          {
            title: 'Web Development Fundamentals',
            description: 'Introduction to web development and core technologies',
            order: 1,
            duration: 300,
            isPublished: true,
            sections: [
              { title: 'How the Web Works', duration: 30, order: 1, isPublished: true },
              { title: 'HTML Fundamentals', duration: 45, order: 2, isPublished: true },
              { title: 'CSS Styling and Layout', duration: 60, order: 3, isPublished: true },
              { title: 'Responsive Design with CSS Grid and Flexbox', duration: 45, order: 4, isPublished: true },
              { title: 'JavaScript Basics', duration: 60, order: 5, isPublished: true },
              { title: 'DOM Manipulation', duration: 40, order: 6, isPublished: true },
              { title: 'ES6+ Features', duration: 20, order: 7, isPublished: true }
            ]
          },
          {
            title: 'Frontend Development with React',
            description: 'Building modern user interfaces with React',
            order: 2,
            duration: 480,
            isPublished: true,
            sections: [
              { title: 'Introduction to React', duration: 30, order: 1, isPublished: true },
              { title: 'Components and JSX', duration: 45, order: 2, isPublished: true },
              { title: 'Props and State', duration: 60, order: 3, isPublished: true },
              { title: 'Event Handling', duration: 30, order: 4, isPublished: true },
              { title: 'React Hooks', duration: 90, order: 5, isPublished: true },
              { title: 'Context API and State Management', duration: 60, order: 6, isPublished: true },
              { title: 'React Router', duration: 45, order: 7, isPublished: true },
              { title: 'API Integration', duration: 60, order: 8, isPublished: true },
              { title: 'Testing React Components', duration: 60, order: 9, isPublished: true }
            ]
          },
          {
            title: 'Backend Development with Node.js',
            description: 'Server-side development and API creation',
            order: 3,
            duration: 420,
            isPublished: true,
            sections: [
              { title: 'Node.js Fundamentals', duration: 45, order: 1, isPublished: true },
              { title: 'Express.js Framework', duration: 60, order: 2, isPublished: true },
              { title: 'RESTful API Design', duration: 75, order: 3, isPublished: true },
              { title: 'Middleware and Error Handling', duration: 45, order: 4, isPublished: true },
              { title: 'Authentication and Authorization', duration: 90, order: 5, isPublished: true },
              { title: 'File Upload and Processing', duration: 30, order: 6, isPublished: true },
              { title: 'API Documentation', duration: 30, order: 7, isPublished: true },
              { title: 'Testing APIs', duration: 45, order: 8, isPublished: true }
            ]
          },
          {
            title: 'Database Integration',
            description: 'Working with databases and data modeling',
            order: 4,
            duration: 240,
            isPublished: true,
            sections: [
              { title: 'Database Fundamentals', duration: 30, order: 1, isPublished: true },
              { title: 'MongoDB and NoSQL', duration: 45, order: 2, isPublished: true },
              { title: 'Mongoose ODM', duration: 60, order: 3, isPublished: true },
              { title: 'Data Modeling and Relationships', duration: 45, order: 4, isPublished: true },
              { title: 'Database Queries and Aggregation', duration: 60, order: 5, isPublished: true }
            ]
          },
          {
            title: 'Deployment and DevOps',
            description: 'Deploying applications to production',
            order: 5,
            duration: 180,
            isPublished: true,
            sections: [
              { title: 'Git and Version Control', duration: 30, order: 1, isPublished: true },
              { title: 'Environment Configuration', duration: 30, order: 2, isPublished: true },
              { title: 'Cloud Deployment (Heroku, Vercel)', duration: 60, order: 3, isPublished: true },
              { title: 'Docker Containerization', duration: 45, order: 4, isPublished: true },
              { title: 'CI/CD Pipelines', duration: 15, order: 5, isPublished: true }
            ]
          }
        ],
        enrollmentCount: 2100,
        rating: 4.6,
        reviews: []
      },
      {
        title: 'Advanced JavaScript and Modern Web APIs',
        description: 'Deep dive into advanced JavaScript concepts and modern web APIs.',
        longDescription: `Master advanced JavaScript concepts including closures, prototypes, async programming, 
        and modern web APIs. This course is perfect for developers who want to level up their JavaScript skills 
        and understand how modern web applications work under the hood.`,
        instructor: instructors[1]?._id,
        category: 'JavaScript',
        level: 'advanced',
        price: 2499,
        originalPrice: 3999,
        duration: 25,
        language: 'english',
        thumbnail: 'https://images.unsplash.com/photo-1579468118864-1b9ea3c0db4a?w=800',
        status: 'published',
        isActive: true,
        settings: {
          isPublished: true,
          allowComments: true,
          allowDownloads: true,
          prerequisites: ['Solid understanding of JavaScript basics', 'Experience with ES6+ features'],
          learningOutcomes: [
            'Master advanced JavaScript concepts',
            'Understand closures, prototypes, and inheritance',
            'Work with modern async patterns',
            'Use Web APIs effectively',
            'Optimize JavaScript performance',
            'Debug complex JavaScript applications'
          ]
        },
        chapters: [
          {
            title: 'Advanced JavaScript Concepts',
            description: 'Deep dive into JavaScript internals',
            order: 1,
            duration: 360,
            isPublished: true,
            sections: [
              { title: 'Execution Context and Hoisting', duration: 45, order: 1, isPublished: true },
              { title: 'Closures and Lexical Scope', duration: 60, order: 2, isPublished: true },
              { title: 'Prototypes and Inheritance', duration: 75, order: 3, isPublished: true },
              { title: 'this Keyword and Binding', duration: 45, order: 4, isPublished: true },
              { title: 'Generators and Iterators', duration: 45, order: 5, isPublished: true },
              { title: 'Symbols and Well-known Symbols', duration: 30, order: 6, isPublished: true },
              { title: 'Proxy and Reflect', duration: 60, order: 7, isPublished: true }
            ]
          },
          {
            title: 'Asynchronous JavaScript',
            description: 'Mastering async programming patterns',
            order: 2,
            duration: 300,
            isPublished: true,
            sections: [
              { title: 'Event Loop and Call Stack', duration: 45, order: 1, isPublished: true },
              { title: 'Promises and Promise Chaining', duration: 60, order: 2, isPublished: true },
              { title: 'Async/Await Patterns', duration: 45, order: 3, isPublished: true },
              { title: 'Error Handling in Async Code', duration: 30, order: 4, isPublished: true },
              { title: 'Concurrent Programming', duration: 45, order: 5, isPublished: true },
              { title: 'Web Workers and Service Workers', duration: 75, order: 6, isPublished: true }
            ]
          },
          {
            title: 'Modern Web APIs',
            description: 'Working with browser APIs and web standards',
            order: 3,
            duration: 240,
            isPublished: true,
            sections: [
              { title: 'Fetch API and HTTP Requests', duration: 45, order: 1, isPublished: true },
              { title: 'Local Storage and Session Storage', duration: 30, order: 2, isPublished: true },
              { title: 'IndexedDB for Client-side Databases', duration: 60, order: 3, isPublished: true },
              { title: 'Geolocation and Device APIs', duration: 30, order: 4, isPublished: true },
              { title: 'WebRTC and Real-time Communication', duration: 45, order: 5, isPublished: true },
              { title: 'Progressive Web App APIs', duration: 30, order: 6, isPublished: true }
            ]
          }
        ],
        enrollmentCount: 750,
        rating: 4.8,
        reviews: []
      },
      {
        title: 'Mobile App Development with React Native',
        description: 'Build native mobile apps for iOS and Android using React Native.',
        longDescription: `Learn to build cross-platform mobile applications using React Native. 
        This course covers everything from setup to deployment, including navigation, state management, 
        native modules, and publishing to app stores.`,
        instructor: instructors[2]?._id,
        category: 'Mobile Development',
        level: 'intermediate',
        price: 3499,
        originalPrice: 5499,
        duration: 35,
        language: 'english',
        thumbnail: 'https://images.unsplash.com/photo-*************-90a1b58e7e9c?w=800',
        status: 'published',
        isActive: true,
        settings: {
          isPublished: true,
          allowComments: true,
          allowDownloads: true,
          prerequisites: ['React.js knowledge', 'JavaScript ES6+', 'Basic mobile development concepts'],
          learningOutcomes: [
            'Build cross-platform mobile apps',
            'Master React Native components and APIs',
            'Implement navigation and routing',
            'Handle device features and permissions',
            'Integrate with backend APIs',
            'Publish apps to App Store and Google Play'
          ]
        },
        chapters: [
          {
            title: 'React Native Fundamentals',
            description: 'Getting started with React Native development',
            order: 1,
            duration: 240,
            isPublished: true,
            sections: [
              { title: 'React Native Setup and Environment', duration: 45, order: 1, isPublished: true },
              { title: 'Core Components and Styling', duration: 60, order: 2, isPublished: true },
              { title: 'Flexbox Layout in React Native', duration: 45, order: 3, isPublished: true },
              { title: 'Handling User Input', duration: 30, order: 4, isPublished: true },
              { title: 'Lists and ScrollViews', duration: 30, order: 5, isPublished: true },
              { title: 'Images and Media', duration: 30, order: 6, isPublished: true }
            ]
          },
          {
            title: 'Navigation and State Management',
            description: 'Building complex app structures',
            order: 2,
            duration: 300,
            isPublished: true,
            sections: [
              { title: 'React Navigation Setup', duration: 45, order: 1, isPublished: true },
              { title: 'Stack, Tab, and Drawer Navigation', duration: 75, order: 2, isPublished: true },
              { title: 'State Management with Context', duration: 60, order: 3, isPublished: true },
              { title: 'Redux for React Native', duration: 90, order: 4, isPublished: true },
              { title: 'Async Storage and Persistence', duration: 30, order: 5, isPublished: true }
            ]
          },
          {
            title: 'Native Features and APIs',
            description: 'Accessing device capabilities',
            order: 3,
            duration: 360,
            isPublished: true,
            sections: [
              { title: 'Camera and Photo Library', duration: 60, order: 1, isPublished: true },
              { title: 'Location Services', duration: 45, order: 2, isPublished: true },
              { title: 'Push Notifications', duration: 75, order: 3, isPublished: true },
              { title: 'Device Sensors and Hardware', duration: 45, order: 4, isPublished: true },
              { title: 'Biometric Authentication', duration: 45, order: 5, isPublished: true },
              { title: 'Native Modules and Bridges', duration: 90, order: 6, isPublished: true }
            ]
          }
        ],
        enrollmentCount: 450,
        rating: 4.4,
        reviews: []
      },
      {
        title: 'DevOps and Cloud Infrastructure',
        description: 'Learn modern DevOps practices and cloud deployment strategies.',
        longDescription: `Master DevOps practices including CI/CD, containerization, cloud services, 
        monitoring, and infrastructure as code. Perfect for developers who want to understand 
        the full software delivery lifecycle.`,
        instructor: instructors[0]?._id,
        category: 'DevOps',
        level: 'advanced',
        price: 4499,
        originalPrice: 6999,
        duration: 40,
        language: 'english',
        thumbnail: 'https://images.unsplash.com/photo-1667372393119-3d4c48d07fc9?w=800',
        status: 'published',
        isActive: true,
        settings: {
          isPublished: true,
          allowComments: true,
          allowDownloads: true,
          prerequisites: ['Linux command line basics', 'Understanding of web applications', 'Basic networking knowledge'],
          learningOutcomes: [
            'Implement CI/CD pipelines',
            'Master Docker and Kubernetes',
            'Deploy to major cloud platforms',
            'Monitor and log applications',
            'Implement infrastructure as code',
            'Ensure security and compliance'
          ]
        },
        chapters: [
          {
            title: 'DevOps Fundamentals',
            description: 'Introduction to DevOps culture and practices',
            order: 1,
            duration: 180,
            isPublished: true,
            sections: [
              { title: 'DevOps Culture and Principles', duration: 30, order: 1, isPublished: true },
              { title: 'Version Control with Git', duration: 45, order: 2, isPublished: true },
              { title: 'Linux Command Line Mastery', duration: 60, order: 3, isPublished: true },
              { title: 'Shell Scripting and Automation', duration: 45, order: 4, isPublished: true }
            ]
          },
          {
            title: 'Containerization and Orchestration',
            description: 'Docker and Kubernetes mastery',
            order: 2,
            duration: 480,
            isPublished: true,
            sections: [
              { title: 'Docker Fundamentals', duration: 90, order: 1, isPublished: true },
              { title: 'Docker Compose and Multi-container Apps', duration: 60, order: 2, isPublished: true },
              { title: 'Kubernetes Architecture', duration: 75, order: 3, isPublished: true },
              { title: 'Kubernetes Deployments and Services', duration: 90, order: 4, isPublished: true },
              { title: 'Kubernetes Networking and Storage', duration: 75, order: 5, isPublished: true },
              { title: 'Helm Charts and Package Management', duration: 60, order: 6, isPublished: true },
              { title: 'Kubernetes Security', duration: 30, order: 7, isPublished: true }
            ]
          },
          {
            title: 'CI/CD and Automation',
            description: 'Building automated deployment pipelines',
            order: 3,
            duration: 300,
            isPublished: true,
            sections: [
              { title: 'CI/CD Concepts and Best Practices', duration: 45, order: 1, isPublished: true },
              { title: 'GitHub Actions', duration: 75, order: 2, isPublished: true },
              { title: 'Jenkins Pipeline', duration: 90, order: 3, isPublished: true },
              { title: 'Testing in CI/CD', duration: 45, order: 4, isPublished: true },
              { title: 'Deployment Strategies', duration: 45, order: 5, isPublished: true }
            ]
          }
        ],
        enrollmentCount: 320,
        rating: 4.7,
        reviews: []
      }
    ];
  }

  async seed() {
    try {
      logger.info('Starting course seeding...');
      
      const instructors = await this.getInstructors();
      
      if (instructors.length === 0) {
        logger.warn('No instructors found. Please seed users first.');
        return [];
      }
      
      const courseData = this.generateCourseData(instructors);
      
      // Clear existing courses
      await Course.deleteMany({});
      logger.info('Cleared existing courses');
      
      // Create new courses
      const courses = await Course.insertMany(courseData);
      logger.info(`Created ${courses.length} courses`);
      
      this.courses = courses;
      return courses;
      
    } catch (error) {
      logger.error('Course seeding failed:', error);
      throw error;
    }
  }

  async addReviews() {
    try {
      logger.info('Adding sample reviews to courses...');
      
      const students = await User.find({ role: 'student' }).limit(10);
      
      if (students.length === 0) {
        logger.warn('No students found for reviews');
        return;
      }
      
      const sampleReviews = [
        { rating: 5, comment: 'Excellent course! Very comprehensive and well-structured.' },
        { rating: 4, comment: 'Great content and practical examples. Highly recommended.' },
        { rating: 5, comment: 'The instructor explains concepts very clearly. Loved it!' },
        { rating: 4, comment: 'Good course with hands-on projects. Worth the investment.' },
        { rating: 5, comment: 'Best course I\'ve taken on this topic. Thank you!' },
        { rating: 4, comment: 'Well organized content with excellent support materials.' },
        { rating: 5, comment: 'Practical approach with real-world examples. Fantastic!' },
        { rating: 4, comment: 'Clear explanations and good pacing throughout the course.' }
      ];
      
      for (const course of this.courses) {
        const numReviews = Math.floor(Math.random() * 5) + 3; // 3-7 reviews per course
        const courseReviews = [];
        
        for (let i = 0; i < numReviews; i++) {
          const randomStudent = students[Math.floor(Math.random() * students.length)];
          const randomReview = sampleReviews[Math.floor(Math.random() * sampleReviews.length)];
          
          courseReviews.push({
            user: randomStudent._id,
            rating: randomReview.rating,
            comment: randomReview.comment,
            createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random date within last 30 days
          });
        }
        
        course.reviews = courseReviews;
        course.calculateAverageRating();
        await course.save();
      }
      
      logger.info('Added sample reviews to courses');
      
    } catch (error) {
      logger.error('Error adding reviews:', error);
      throw error;
    }
  }

  async run() {
    const courses = await this.seed();
    await this.addReviews();
    return courses;
  }
}

module.exports = CourseSeeder;
