const Course = require('../models/Course');
const User = require('../models/User');
const logger = require('../utils/logger');
const { ValidationError, NotFoundError, ConflictError } = require('../utils/errors');

/**
 * Course Service
 * Handles all course-related business logic
 */
class CourseService {
  /**
   * Create a new course
   * @param {Object} courseData - Course data
   * @param {String} instructorId - Instructor ID
   * @returns {Object} Created course
   */
  static async createCourse(courseData, instructorId) {
    try {
      // Verify instructor exists and has correct role
      const instructor = await User.findById(instructorId);
      if (!instructor) {
        throw new NotFoundError('Instructor not found');
      }

      if (instructor.role !== 'instructor' && instructor.role !== 'admin') {
        throw new ValidationError('Only instructors and admins can create courses');
      }

      // Check for duplicate course title by same instructor
      const existingCourse = await Course.findOne({
        title: courseData.title,
        instructor: instructorId
      });

      if (existingCourse) {
        throw new ConflictError('Course with this title already exists');
      }

      // Create course
      const course = new Course({
        ...courseData,
        instructor: instructorId
      });

      await course.save();

      // Populate instructor data
      await course.populate('instructor', 'firstName lastName email avatar');

      logger.info(`Course created successfully: ${course.title} by ${instructor.email}`);
      return course;
    } catch (error) {
      logger.error('Error creating course:', error);
      throw error;
    }
  }

  /**
   * Get courses with filters and pagination
   * @param {Object} filters - Filter criteria
   * @param {Object} options - Query options
   * @returns {Object} Courses and pagination info
   */
  static async getCourses(filters = {}, options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        sort = 'createdAt',
        order = 'desc',
        search,
        category,
        level,
        instructor,
        status = 'published'
      } = options;

      // Build query
      const query = { isActive: true, ...filters };

      if (status) {
        query.status = status;
      }

      if (category) {
        query.category = category;
      }

      if (level) {
        query.level = level;
      }

      if (instructor) {
        query.instructor = instructor;
      }

      if (search) {
        query.$text = { $search: search };
      }

      // Execute query
      const courses = await Course.find(query)
        .populate('instructor', 'firstName lastName avatar')
        .sort({ [sort]: order === 'desc' ? -1 : 1 })
        .skip((page - 1) * limit)
        .limit(limit);

      const total = await Course.countDocuments(query);

      return {
        courses,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error fetching courses:', error);
      throw error;
    }
  }

  /**
   * Get course by ID
   * @param {String} courseId - Course ID
   * @param {String} userId - User ID (optional, for enrollment status)
   * @returns {Object} Course data
   */
  static async getCourseById(courseId, userId = null) {
    try {
      const course = await Course.findById(courseId)
        .populate('instructor', 'firstName lastName email avatar bio')
        .populate('reviews.user', 'firstName lastName avatar');

      if (!course) {
        throw new NotFoundError('Course not found');
      }

      const courseData = course.toObject();

      // Check if user is enrolled (if userId provided)
      if (userId) {
        const user = await User.findById(userId);
        if (user) {
          const enrollment = user.enrolledCourses.find(
            enrollment => enrollment.course.toString() === courseId
          );
          courseData.isEnrolled = !!enrollment;
          courseData.userProgress = enrollment ? enrollment.progress : 0;
        }
      }

      return courseData;
    } catch (error) {
      logger.error('Error fetching course:', error);
      throw error;
    }
  }

  /**
   * Update course
   * @param {String} courseId - Course ID
   * @param {Object} updateData - Update data
   * @param {String} userId - User ID (for authorization)
   * @returns {Object} Updated course
   */
  static async updateCourse(courseId, updateData, userId) {
    try {
      const course = await Course.findById(courseId);

      if (!course) {
        throw new NotFoundError('Course not found');
      }

      // Check authorization
      const user = await User.findById(userId);
      if (!user) {
        throw new NotFoundError('User not found');
      }

      if (course.instructor.toString() !== userId && user.role !== 'admin') {
        throw new ValidationError('Not authorized to update this course');
      }

      // Update course
      const updatedCourse = await Course.findByIdAndUpdate(
        courseId,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).populate('instructor', 'firstName lastName email avatar');

      logger.info(`Course updated successfully: ${updatedCourse.title}`);
      return updatedCourse;
    } catch (error) {
      logger.error('Error updating course:', error);
      throw error;
    }
  }

  /**
   * Delete course
   * @param {String} courseId - Course ID
   * @param {String} userId - User ID (for authorization)
   * @returns {Boolean} Success status
   */
  static async deleteCourse(courseId, userId) {
    try {
      const course = await Course.findById(courseId);

      if (!course) {
        throw new NotFoundError('Course not found');
      }

      // Check authorization
      const user = await User.findById(userId);
      if (!user) {
        throw new NotFoundError('User not found');
      }

      if (course.instructor.toString() !== userId && user.role !== 'admin') {
        throw new ValidationError('Not authorized to delete this course');
      }

      // Soft delete (mark as inactive)
      await Course.findByIdAndUpdate(courseId, {
        isActive: false,
        updatedAt: new Date()
      });

      logger.info(`Course deleted successfully: ${course.title}`);
      return true;
    } catch (error) {
      logger.error('Error deleting course:', error);
      throw error;
    }
  }

  /**
   * Add review to course
   * @param {String} courseId - Course ID
   * @param {String} userId - User ID
   * @param {Object} reviewData - Review data
   * @returns {Object} Updated course
   */
  static async addReview(courseId, userId, reviewData) {
    try {
      const course = await Course.findById(courseId);

      if (!course) {
        throw new NotFoundError('Course not found');
      }

      // Check if user is enrolled
      const user = await User.findById(userId);
      if (!user) {
        throw new NotFoundError('User not found');
      }

      const enrollment = user.enrolledCourses.find(
        enrollment => enrollment.course.toString() === courseId
      );

      if (!enrollment) {
        throw new ValidationError('You must be enrolled in the course to leave a review');
      }

      // Check if user already reviewed
      const existingReview = course.reviews.find(
        review => review.user.toString() === userId
      );

      if (existingReview) {
        throw new ConflictError('You have already reviewed this course');
      }

      // Add review
      course.reviews.push({
        user: userId,
        rating: reviewData.rating,
        comment: reviewData.comment,
        createdAt: new Date()
      });

      // Recalculate average rating
      course.calculateAverageRating();

      await course.save();

      // Populate the new review
      await course.populate('reviews.user', 'firstName lastName avatar');

      logger.info(`Review added to course ${course.title} by user ${user.email}`);
      return course;
    } catch (error) {
      logger.error('Error adding review:', error);
      throw error;
    }
  }

  /**
   * Get course analytics
   * @param {String} courseId - Course ID
   * @param {String} instructorId - Instructor ID (for authorization)
   * @returns {Object} Course analytics
   */
  static async getCourseAnalytics(courseId, instructorId) {
    try {
      const course = await Course.findById(courseId);

      if (!course) {
        throw new NotFoundError('Course not found');
      }

      // Check authorization
      const instructor = await User.findById(instructorId);
      if (!instructor) {
        throw new NotFoundError('Instructor not found');
      }

      if (course.instructor.toString() !== instructorId && instructor.role !== 'admin') {
        throw new ValidationError('Not authorized to view course analytics');
      }

      // Get enrollment data
      const enrolledUsers = await User.find({
        'enrolledCourses.course': courseId
      }).select('enrolledCourses firstName lastName email createdAt');

      const enrollments = enrolledUsers.map(user => {
        const enrollment = user.enrolledCourses.find(
          e => e.course.toString() === courseId
        );
        return {
          user: {
            id: user._id,
            name: `${user.firstName} ${user.lastName}`,
            email: user.email
          },
          enrollmentDate: enrollment.enrollmentDate,
          progress: enrollment.progress,
          completedAt: enrollment.completedAt,
          lastAccessedAt: enrollment.lastAccessedAt
        };
      });

      // Calculate analytics
      const totalEnrollments = enrollments.length;
      const completedCount = enrollments.filter(e => e.progress === 100).length;
      const averageProgress = enrollments.length > 0 
        ? enrollments.reduce((sum, e) => sum + e.progress, 0) / enrollments.length 
        : 0;

      const analytics = {
        course: {
          id: course._id,
          title: course.title,
          createdAt: course.createdAt
        },
        enrollments: {
          total: totalEnrollments,
          completed: completedCount,
          inProgress: totalEnrollments - completedCount,
          completionRate: totalEnrollments > 0 ? (completedCount / totalEnrollments) * 100 : 0
        },
        progress: {
          average: Math.round(averageProgress),
          distribution: {
            '0-25%': enrollments.filter(e => e.progress >= 0 && e.progress < 25).length,
            '25-50%': enrollments.filter(e => e.progress >= 25 && e.progress < 50).length,
            '50-75%': enrollments.filter(e => e.progress >= 75 && e.progress < 75).length,
            '75-100%': enrollments.filter(e => e.progress >= 75 && e.progress <= 100).length
          }
        },
        reviews: {
          total: course.reviews.length,
          averageRating: course.rating,
          ratingDistribution: {
            5: course.reviews.filter(r => r.rating === 5).length,
            4: course.reviews.filter(r => r.rating === 4).length,
            3: course.reviews.filter(r => r.rating === 3).length,
            2: course.reviews.filter(r => r.rating === 2).length,
            1: course.reviews.filter(r => r.rating === 1).length
          }
        },
        students: enrollments
      };

      return analytics;
    } catch (error) {
      logger.error('Error fetching course analytics:', error);
      throw error;
    }
  }

  /**
   * Get featured courses
   * @param {Number} limit - Number of courses to return
   * @returns {Array} Featured courses
   */
  static async getFeaturedCourses(limit = 6) {
    try {
      const courses = await Course.find({
        status: 'published',
        isActive: true,
        rating: { $gte: 4.0 }
      })
        .populate('instructor', 'firstName lastName avatar')
        .sort({ rating: -1, enrollmentCount: -1 })
        .limit(limit);

      return courses;
    } catch (error) {
      logger.error('Error fetching featured courses:', error);
      throw error;
    }
  }

  /**
   * Search courses
   * @param {String} searchTerm - Search term
   * @param {Object} filters - Additional filters
   * @param {Object} options - Query options
   * @returns {Object} Search results
   */
  static async searchCourses(searchTerm, filters = {}, options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        sort = 'relevance'
      } = options;

      // Build search query
      const query = {
        $text: { $search: searchTerm },
        status: 'published',
        isActive: true,
        ...filters
      };

      // Determine sort order
      let sortOrder = {};
      if (sort === 'relevance') {
        sortOrder = { score: { $meta: 'textScore' } };
      } else if (sort === 'rating') {
        sortOrder = { rating: -1 };
      } else if (sort === 'price') {
        sortOrder = { price: 1 };
      } else {
        sortOrder = { createdAt: -1 };
      }

      const courses = await Course.find(query)
        .populate('instructor', 'firstName lastName avatar')
        .sort(sortOrder)
        .skip((page - 1) * limit)
        .limit(limit);

      const total = await Course.countDocuments(query);

      return {
        courses,
        searchTerm,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error searching courses:', error);
      throw error;
    }
  }
}

module.exports = CourseService;
