const mongoose = require('mongoose');
const fs = require('fs').promises;
const path = require('path');
const config = require('../src/config/config');
const logger = require('../src/utils/logger');

/**
 * Database Migration Runner
 * Handles running and tracking database migrations
 */

// Migration tracking schema
const migrationSchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true },
  description: { type: String },
  executedAt: { type: Date, default: Date.now },
  executionTime: { type: Number }, // in milliseconds
  status: { type: String, enum: ['completed', 'failed'], default: 'completed' }
});

const Migration = mongoose.model('Migration', migrationSchema);

class MigrationRunner {
  constructor() {
    this.migrationsPath = path.join(__dirname, 'migrations');
  }

  async connect() {
    try {
      await mongoose.connect(config.MONGODB_URI, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });
      logger.info('Connected to MongoDB for migrations');
    } catch (error) {
      logger.error('Database connection failed:', error);
      throw error;
    }
  }

  async disconnect() {
    await mongoose.connection.close();
    logger.info('Database connection closed');
  }

  async getMigrationFiles() {
    try {
      const files = await fs.readdir(this.migrationsPath);
      return files
        .filter(file => file.endsWith('.js'))
        .sort()
        .map(file => ({
          name: file.replace('.js', ''),
          path: path.join(this.migrationsPath, file)
        }));
    } catch (error) {
      logger.error('Error reading migration files:', error);
      return [];
    }
  }

  async getExecutedMigrations() {
    try {
      const executed = await Migration.find({}, 'name').sort({ name: 1 });
      return executed.map(m => m.name);
    } catch (error) {
      logger.error('Error getting executed migrations:', error);
      return [];
    }
  }

  async getPendingMigrations() {
    const allMigrations = await this.getMigrationFiles();
    const executedMigrations = await this.getExecutedMigrations();
    
    return allMigrations.filter(migration => 
      !executedMigrations.includes(migration.name)
    );
  }

  async executeMigration(migration, direction = 'up') {
    const startTime = Date.now();
    
    try {
      logger.info(`Executing migration ${migration.name} (${direction})`);
      
      // Load migration module
      const migrationModule = require(migration.path);
      
      if (typeof migrationModule[direction] !== 'function') {
        throw new Error(`Migration ${migration.name} does not have a ${direction} function`);
      }
      
      // Execute migration
      await migrationModule[direction]();
      
      const executionTime = Date.now() - startTime;
      
      if (direction === 'up') {
        // Record successful migration
        await Migration.create({
          name: migration.name,
          description: migrationModule.description || 'No description provided',
          executionTime,
          status: 'completed'
        });
        
        logger.info(`Migration ${migration.name} completed in ${executionTime}ms`);
      } else {
        // Remove migration record for rollback
        await Migration.deleteOne({ name: migration.name });
        logger.info(`Migration ${migration.name} rolled back in ${executionTime}ms`);
      }
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      if (direction === 'up') {
        // Record failed migration
        try {
          await Migration.create({
            name: migration.name,
            description: 'Migration failed',
            executionTime,
            status: 'failed'
          });
        } catch (recordError) {
          logger.error('Failed to record migration failure:', recordError);
        }
      }
      
      logger.error(`Migration ${migration.name} failed after ${executionTime}ms:`, error);
      throw error;
    }
  }

  async runMigrations() {
    try {
      await this.connect();
      
      const pendingMigrations = await this.getPendingMigrations();
      
      if (pendingMigrations.length === 0) {
        logger.info('No pending migrations to run');
        return;
      }
      
      logger.info(`Found ${pendingMigrations.length} pending migrations`);
      
      for (const migration of pendingMigrations) {
        await this.executeMigration(migration, 'up');
      }
      
      logger.info('All migrations completed successfully');
      
    } catch (error) {
      logger.error('Migration process failed:', error);
      throw error;
    } finally {
      await this.disconnect();
    }
  }

  async rollbackMigration(migrationName) {
    try {
      await this.connect();
      
      const executedMigrations = await this.getExecutedMigrations();
      
      if (!executedMigrations.includes(migrationName)) {
        throw new Error(`Migration ${migrationName} has not been executed`);
      }
      
      const allMigrations = await this.getMigrationFiles();
      const migration = allMigrations.find(m => m.name === migrationName);
      
      if (!migration) {
        throw new Error(`Migration file ${migrationName} not found`);
      }
      
      await this.executeMigration(migration, 'down');
      
      logger.info(`Migration ${migrationName} rolled back successfully`);
      
    } catch (error) {
      logger.error('Rollback failed:', error);
      throw error;
    } finally {
      await this.disconnect();
    }
  }

  async getMigrationStatus() {
    try {
      await this.connect();
      
      const allMigrations = await this.getMigrationFiles();
      const executedMigrations = await Migration.find({}).sort({ name: 1 });
      
      const status = allMigrations.map(migration => {
        const executed = executedMigrations.find(e => e.name === migration.name);
        return {
          name: migration.name,
          status: executed ? executed.status : 'pending',
          executedAt: executed ? executed.executedAt : null,
          executionTime: executed ? executed.executionTime : null,
          description: executed ? executed.description : 'Not executed'
        };
      });
      
      return status;
      
    } catch (error) {
      logger.error('Error getting migration status:', error);
      throw error;
    } finally {
      await this.disconnect();
    }
  }

  async createMigration(name, description = '') {
    try {
      const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0];
      const migrationName = `${timestamp}_${name}`;
      const fileName = `${migrationName}.js`;
      const filePath = path.join(this.migrationsPath, fileName);
      
      const template = `const mongoose = require('mongoose');
const logger = require('../../src/utils/logger');

/**
 * Migration: ${description || name}
 */

async function up() {
  try {
    logger.info('Running migration: ${migrationName}');
    
    // Add your migration logic here
    
    logger.info('Migration ${migrationName} completed successfully');
    
  } catch (error) {
    logger.error('Migration ${migrationName} failed:', error);
    throw error;
  }
}

async function down() {
  try {
    logger.info('Rolling back migration: ${migrationName}');
    
    // Add your rollback logic here
    
    logger.info('Migration ${migrationName} rollback completed');
    
  } catch (error) {
    logger.error('Migration ${migrationName} rollback failed:', error);
    throw error;
  }
}

module.exports = {
  up,
  down,
  description: '${description || name}'
};`;
      
      await fs.writeFile(filePath, template);
      logger.info(`Migration file created: ${fileName}`);
      
      return fileName;
      
    } catch (error) {
      logger.error('Error creating migration file:', error);
      throw error;
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const migrationRunner = new MigrationRunner();
  
  try {
    switch (command) {
      case 'up':
      case 'migrate':
        await migrationRunner.runMigrations();
        break;
        
      case 'down':
      case 'rollback':
        const migrationName = args[1];
        if (!migrationName) {
          throw new Error('Migration name is required for rollback');
        }
        await migrationRunner.rollbackMigration(migrationName);
        break;
        
      case 'status':
        const status = await migrationRunner.getMigrationStatus();
        console.table(status);
        break;
        
      case 'create':
        const name = args[1];
        const description = args[2] || '';
        if (!name) {
          throw new Error('Migration name is required');
        }
        await migrationRunner.createMigration(name, description);
        break;
        
      default:
        console.log('Usage:');
        console.log('  node migrate.js up|migrate          - Run pending migrations');
        console.log('  node migrate.js down|rollback <name> - Rollback specific migration');
        console.log('  node migrate.js status              - Show migration status');
        console.log('  node migrate.js create <name> [desc] - Create new migration file');
        break;
    }
  } catch (error) {
    logger.error('Migration command failed:', error);
    process.exit(1);
  }
}

// Run CLI if called directly
if (require.main === module) {
  main();
}

module.exports = MigrationRunner;
