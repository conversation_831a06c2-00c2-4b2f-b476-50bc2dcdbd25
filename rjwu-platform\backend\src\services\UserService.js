const User = require('../models/User');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const config = require('../config/config');
const logger = require('../utils/logger');
const { ValidationError, NotFoundError, ConflictError } = require('../utils/errors');

/**
 * User Service
 * Handles all user-related business logic
 */
class UserService {
  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Object} Created user
   */
  static async createUser(userData) {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [
          { email: userData.email },
          { phone: userData.phone }
        ]
      });

      if (existingUser) {
        if (existingUser.email === userData.email) {
          throw new ConflictError('User with this email already exists');
        }
        if (existingUser.phone === userData.phone) {
          throw new ConflictError('User with this phone number already exists');
        }
      }

      // Hash password
      const saltRounds = parseInt(config.BCRYPT_ROUNDS) || 12;
      userData.password = await bcrypt.hash(userData.password, saltRounds);

      // Create user
      const user = new User(userData);
      await user.save();

      // Remove password from response
      const userResponse = user.toObject();
      delete userResponse.password;

      logger.info(`User created successfully: ${user.email}`);
      return userResponse;
    } catch (error) {
      logger.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   * @param {String} userId - User ID
   * @returns {Object} User data
   */
  static async getUserById(userId) {
    try {
      const user = await User.findById(userId)
        .populate('enrolledCourses.course', 'title thumbnail price')
        .select('-password');

      if (!user) {
        throw new NotFoundError('User not found');
      }

      return user;
    } catch (error) {
      logger.error('Error fetching user:', error);
      throw error;
    }
  }

  /**
   * Get user by email
   * @param {String} email - User email
   * @returns {Object} User data
   */
  static async getUserByEmail(email) {
    try {
      const user = await User.findOne({ email }).select('+password');
      return user;
    } catch (error) {
      logger.error('Error fetching user by email:', error);
      throw error;
    }
  }

  /**
   * Update user
   * @param {String} userId - User ID
   * @param {Object} updateData - Update data
   * @returns {Object} Updated user
   */
  static async updateUser(userId, updateData) {
    try {
      // Remove sensitive fields from update data
      delete updateData.password;
      delete updateData.role;
      delete updateData.isActive;

      const user = await User.findByIdAndUpdate(
        userId,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).select('-password');

      if (!user) {
        throw new NotFoundError('User not found');
      }

      logger.info(`User updated successfully: ${user.email}`);
      return user;
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Delete user
   * @param {String} userId - User ID
   * @returns {Boolean} Success status
   */
  static async deleteUser(userId) {
    try {
      const user = await User.findByIdAndDelete(userId);

      if (!user) {
        throw new NotFoundError('User not found');
      }

      logger.info(`User deleted successfully: ${user.email}`);
      return true;
    } catch (error) {
      logger.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Change user password
   * @param {String} userId - User ID
   * @param {String} currentPassword - Current password
   * @param {String} newPassword - New password
   * @returns {Boolean} Success status
   */
  static async changePassword(userId, currentPassword, newPassword) {
    try {
      const user = await User.findById(userId).select('+password');

      if (!user) {
        throw new NotFoundError('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new ValidationError('Current password is incorrect');
      }

      // Hash new password
      const saltRounds = parseInt(config.BCRYPT_ROUNDS) || 12;
      const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

      // Update password
      await User.findByIdAndUpdate(userId, {
        password: hashedNewPassword,
        updatedAt: new Date()
      });

      logger.info(`Password changed successfully for user: ${user.email}`);
      return true;
    } catch (error) {
      logger.error('Error changing password:', error);
      throw error;
    }
  }

  /**
   * Get user courses
   * @param {String} userId - User ID
   * @returns {Array} User courses
   */
  static async getUserCourses(userId) {
    try {
      const user = await User.findById(userId)
        .populate({
          path: 'enrolledCourses.course',
          select: 'title description thumbnail price duration level category instructor',
          populate: {
            path: 'instructor',
            select: 'firstName lastName avatar'
          }
        })
        .select('enrolledCourses');

      if (!user) {
        throw new NotFoundError('User not found');
      }

      return user.enrolledCourses;
    } catch (error) {
      logger.error('Error fetching user courses:', error);
      throw error;
    }
  }

  /**
   * Enroll user in course
   * @param {String} userId - User ID
   * @param {String} courseId - Course ID
   * @returns {Object} Enrollment data
   */
  static async enrollUserInCourse(userId, courseId) {
    try {
      const user = await User.findById(userId);

      if (!user) {
        throw new NotFoundError('User not found');
      }

      // Check if already enrolled
      const existingEnrollment = user.enrolledCourses.find(
        enrollment => enrollment.course.toString() === courseId
      );

      if (existingEnrollment) {
        throw new ConflictError('User is already enrolled in this course');
      }

      // Add enrollment
      user.enrolledCourses.push({
        course: courseId,
        enrollmentDate: new Date(),
        progress: 0
      });

      await user.save();

      logger.info(`User ${user.email} enrolled in course ${courseId}`);
      return user.enrolledCourses[user.enrolledCourses.length - 1];
    } catch (error) {
      logger.error('Error enrolling user in course:', error);
      throw error;
    }
  }

  /**
   * Update user course progress
   * @param {String} userId - User ID
   * @param {String} courseId - Course ID
   * @param {Number} progress - Progress percentage
   * @returns {Object} Updated enrollment
   */
  static async updateUserProgress(userId, courseId, progress) {
    try {
      const user = await User.findById(userId);

      if (!user) {
        throw new NotFoundError('User not found');
      }

      const enrollment = user.enrolledCourses.find(
        enrollment => enrollment.course.toString() === courseId
      );

      if (!enrollment) {
        throw new NotFoundError('User is not enrolled in this course');
      }

      // Update progress
      enrollment.progress = Math.min(Math.max(progress, 0), 100);
      enrollment.lastAccessedAt = new Date();

      if (enrollment.progress === 100 && !enrollment.completedAt) {
        enrollment.completedAt = new Date();
      }

      await user.save();

      logger.info(`Progress updated for user ${user.email} in course ${courseId}: ${progress}%`);
      return enrollment;
    } catch (error) {
      logger.error('Error updating user progress:', error);
      throw error;
    }
  }

  /**
   * Get users with filters
   * @param {Object} filters - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Object} Users and pagination info
   */
  static async getUsers(filters = {}, options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        sort = 'createdAt',
        order = 'desc',
        search
      } = options;

      // Build query
      const query = { ...filters };

      if (search) {
        query.$or = [
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ];
      }

      // Execute query
      const users = await User.find(query)
        .select('-password')
        .sort({ [sort]: order === 'desc' ? -1 : 1 })
        .skip((page - 1) * limit)
        .limit(limit);

      const total = await User.countDocuments(query);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error fetching users:', error);
      throw error;
    }
  }

  /**
   * Authenticate user
   * @param {String} email - User email
   * @param {String} password - User password
   * @returns {Object} User and token
   */
  static async authenticateUser(email, password) {
    try {
      const user = await User.findOne({ email }).select('+password');

      if (!user) {
        throw new ValidationError('Invalid email or password');
      }

      if (!user.isActive) {
        throw new ValidationError('Account is deactivated');
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        throw new ValidationError('Invalid email or password');
      }

      // Generate JWT token
      const token = jwt.sign(
        { userId: user._id, email: user.email, role: user.role },
        config.JWT_SECRET,
        { expiresIn: config.JWT_EXPIRES_IN }
      );

      // Remove password from response
      const userResponse = user.toObject();
      delete userResponse.password;

      logger.info(`User authenticated successfully: ${user.email}`);
      return { user: userResponse, token };
    } catch (error) {
      logger.error('Error authenticating user:', error);
      throw error;
    }
  }
}

module.exports = UserService;
