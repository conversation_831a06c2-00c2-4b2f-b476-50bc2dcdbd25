const AnalyticsService = require('../services/AnalyticsService');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

// Get user learning analytics
const getUserLearningAnalytics = async (req, res, next) => {
  try {
    const userId = req.params.userId || req.user.id;
    
    // Check if user can access this data
    if (userId !== req.user.id && req.user.role !== 'admin') {
      return next(new AppError('Access denied', 403));
    }

    const user = await User.findById(userId)
      .populate('enrolledCourses.course', 'title category duration')
      .populate('paymentHistory.course', 'title price');

    if (!user) {
      return next(new AppError('User not found', 404));
    }

    // Calculate learning statistics
    const analytics = {
      overview: {
        totalCourses: user.enrolledCourses.length,
        completedCourses: user.enrolledCourses.filter(c => c.status === 'completed').length,
        activeCourses: user.enrolledCourses.filter(c => c.status === 'active').length,
        totalStudyTime: user.learningStats.totalStudyTime,
        loginStreak: user.learningStats.loginStreak,
        averageScore: user.learningStats.averageScore,
        testsAttempted: user.learningStats.totalTestsAttempted
      },
      progressOverTime: [],
      categoryWiseProgress: {},
      weakAreas: user.learningStats.weakSubjects,
      strongAreas: user.learningStats.strongSubjects,
      achievements: [],
      recommendations: []
    };

    // Calculate category-wise progress
    const categoryStats = {};
    user.enrolledCourses.forEach(enrollment => {
      const category = enrollment.course.category;
      if (!categoryStats[category]) {
        categoryStats[category] = {
          total: 0,
          completed: 0,
          totalProgress: 0
        };
      }
      categoryStats[category].total++;
      categoryStats[category].totalProgress += enrollment.progress;
      if (enrollment.status === 'completed') {
        categoryStats[category].completed++;
      }
    });

    // Calculate average progress for each category
    Object.keys(categoryStats).forEach(category => {
      const stats = categoryStats[category];
      analytics.categoryWiseProgress[category] = {
        totalCourses: stats.total,
        completedCourses: stats.completed,
        averageProgress: Math.round(stats.totalProgress / stats.total),
        completionRate: Math.round((stats.completed / stats.total) * 100)
      };
    });

    // Generate achievements
    if (analytics.overview.loginStreak >= 7) {
      analytics.achievements.push({
        title: '7-Day Streak',
        description: 'Logged in for 7 consecutive days',
        icon: 'streak',
        earnedAt: user.learningStats.lastLoginDate
      });
    }

    if (analytics.overview.loginStreak >= 30) {
      analytics.achievements.push({
        title: '30-Day Streak',
        description: 'Logged in for 30 consecutive days',
        icon: 'streak-gold',
        earnedAt: user.learningStats.lastLoginDate
      });
    }

    if (analytics.overview.completedCourses >= 1) {
      analytics.achievements.push({
        title: 'First Course Completed',
        description: 'Completed your first course',
        icon: 'first-course',
        earnedAt: user.enrolledCourses.find(c => c.status === 'completed')?.lastAccessed
      });
    }

    if (analytics.overview.completedCourses >= 5) {
      analytics.achievements.push({
        title: '5 Courses Master',
        description: 'Completed 5 courses',
        icon: 'master',
        earnedAt: new Date()
      });
    }

    // Generate recommendations
    if (analytics.overview.completedCourses === 0 && analytics.overview.activeCourses > 0) {
      analytics.recommendations.push({
        type: 'motivation',
        message: 'You\'re doing great! Keep going to complete your first course.',
        action: 'Continue Learning'
      });
    }

    if (user.learningStats.weakSubjects.length > 0) {
      analytics.recommendations.push({
        type: 'improvement',
        message: `Focus on improving in ${user.learningStats.weakSubjects.join(', ')}`,
        action: 'Find Practice Tests'
      });
    }

    res.status(200).json({
      status: 'success',
      data: {
        user: {
          name: user.fullName,
          memberSince: user.createdAt
        },
        analytics
      }
    });
  } catch (error) {
    logger.error('Get user learning analytics error:', error);
    next(error);
  }
};

// Get course analytics (instructor/admin only)
const getCourseAnalytics = async (req, res, next) => {
  try {
    const { courseId } = req.params;
    const { period = '30' } = req.query; // days
    
    const course = await Course.findById(courseId)
      .populate('instructor', 'firstName lastName email');

    if (!course) {
      return next(new AppError('Course not found', 404));
    }

    // Check access permissions
    if (req.user.role !== 'admin' && course.instructor._id.toString() !== req.user.id) {
      return next(new AppError('Access denied', 403));
    }

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Get enrollments over time
    const enrollmentStats = await User.aggregate([
      { $unwind: '$enrolledCourses' },
      { $match: { 'enrolledCourses.course': course._id } },
      {
        $group: {
          _id: {
            year: { $year: '$enrolledCourses.enrollmentDate' },
            month: { $month: '$enrolledCourses.enrollmentDate' },
            day: { $dayOfMonth: '$enrolledCourses.enrollmentDate' }
          },
          enrollments: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Get completion rates
    const completionStats = await User.aggregate([
      { $unwind: '$enrolledCourses' },
      { $match: { 'enrolledCourses.course': course._id } },
      {
        $group: {
          _id: '$enrolledCourses.status',
          count: { $sum: 1 },
          averageProgress: { $avg: '$enrolledCourses.progress' }
        }
      }
    ]);

    // Get revenue data
    const revenueStats = await Payment.aggregate([
      { $match: { course: course._id, status: 'completed' } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          revenue: { $sum: '$amount' },
          transactions: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Get student demographics
    const demographicsStats = await User.aggregate([
      { $unwind: '$enrolledCourses' },
      { $match: { 'enrolledCourses.course': course._id } },
      {
        $group: {
          _id: {
            gender: '$gender',
            education: '$education.highestQualification'
          },
          count: { $sum: 1 }
        }
      }
    ]);

    // Calculate overall statistics
    const totalEnrollments = course.enrollmentCount;
    const totalRevenue = await Payment.aggregate([
      { $match: { course: course._id, status: 'completed' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    const completionRate = completionStats.find(stat => stat._id === 'completed')?.count || 0;
    const dropoutRate = completionStats.find(stat => stat._id === 'cancelled')?.count || 0;

    const analytics = {
      overview: {
        totalEnrollments,
        totalRevenue: totalRevenue[0]?.total || 0,
        completionRate: totalEnrollments > 0 ? Math.round((completionRate / totalEnrollments) * 100) : 0,
        dropoutRate: totalEnrollments > 0 ? Math.round((dropoutRate / totalEnrollments) * 100) : 0,
        averageRating: course.rating,
        totalReviews: course.reviews.length
      },
      enrollmentTrends: enrollmentStats,
      completionStats,
      revenueStats,
      demographics: demographicsStats,
      topPerformingChapters: [],
      studentFeedback: course.reviews.slice(-10) // Last 10 reviews
    };

    res.status(200).json({
      status: 'success',
      data: {
        course: {
          id: course._id,
          title: course.title,
          instructor: course.instructor
        },
        analytics
      }
    });
  } catch (error) {
    logger.error('Get course analytics error:', error);
    next(error);
  }
};

// Get instructor analytics
const getInstructorAnalytics = async (req, res, next) => {
  try {
    const instructorId = req.params.instructorId || req.user.id;
    
    // Check access permissions
    if (instructorId !== req.user.id && req.user.role !== 'admin') {
      return next(new AppError('Access denied', 403));
    }

    const instructor = await User.findById(instructorId);
    if (!instructor || instructor.role !== 'instructor') {
      return next(new AppError('Instructor not found', 404));
    }

    const { period = '30' } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Get instructor's courses
    const courses = await Course.find({ instructor: instructorId, isActive: true })
      .select('title enrollmentCount rating price');

    const courseIds = courses.map(course => course._id);

    // Calculate total students across all courses
    const totalStudents = await User.aggregate([
      { $unwind: '$enrolledCourses' },
      { $match: { 'enrolledCourses.course': { $in: courseIds } } },
      { $group: { _id: '$_id' } },
      { $count: 'uniqueStudents' }
    ]);

    // Calculate total revenue
    const totalRevenue = await Payment.aggregate([
      { $match: { course: { $in: courseIds }, status: 'completed' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    // Get monthly revenue trends
    const monthlyRevenue = await Payment.aggregate([
      { 
        $match: { 
          course: { $in: courseIds }, 
          status: 'completed',
          createdAt: { $gte: startDate }
        } 
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          revenue: { $sum: '$amount' },
          transactions: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    // Get course performance
    const coursePerformance = courses.map(course => ({
      id: course._id,
      title: course.title,
      enrollments: course.enrollmentCount,
      rating: course.rating,
      revenue: 0 // Will be calculated below
    }));

    // Calculate revenue per course
    const revenuePerCourse = await Payment.aggregate([
      { $match: { course: { $in: courseIds }, status: 'completed' } },
      {
        $group: {
          _id: '$course',
          revenue: { $sum: '$amount' },
          transactions: { $sum: 1 }
        }
      }
    ]);

    // Update course performance with revenue data
    revenuePerCourse.forEach(revenue => {
      const course = coursePerformance.find(c => c.id.toString() === revenue._id.toString());
      if (course) {
        course.revenue = revenue.revenue;
        course.transactions = revenue.transactions;
      }
    });

    // Get student engagement data
    const engagementStats = await User.aggregate([
      { $unwind: '$enrolledCourses' },
      { $match: { 'enrolledCourses.course': { $in: courseIds } } },
      {
        $group: {
          _id: '$enrolledCourses.status',
          count: { $sum: 1 },
          averageProgress: { $avg: '$enrolledCourses.progress' }
        }
      }
    ]);

    const analytics = {
      overview: {
        totalCourses: courses.length,
        totalStudents: totalStudents[0]?.uniqueStudents || 0,
        totalRevenue: totalRevenue[0]?.total || 0,
        averageRating: courses.reduce((sum, course) => sum + course.rating, 0) / courses.length || 0,
        instructorRating: instructor.instructorProfile.rating
      },
      monthlyRevenue,
      coursePerformance: coursePerformance.sort((a, b) => b.revenue - a.revenue),
      studentEngagement: engagementStats,
      recentActivity: [] // Can be extended to show recent enrollments, reviews, etc.
    };

    res.status(200).json({
      status: 'success',
      data: {
        instructor: {
          id: instructor._id,
          name: instructor.fullName,
          experience: instructor.instructorProfile.experience,
          specializations: instructor.instructorProfile.specializations
        },
        analytics
      }
    });
  } catch (error) {
    logger.error('Get instructor analytics error:', error);
    next(error);
  }
};

// Get platform analytics (admin only)
const getPlatformAnalytics = async (req, res, next) => {
  try {
    const { period = '30' } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    const filters = {
      startDate: startDate.toISOString(),
      endDate: new Date().toISOString()
    };

    const analytics = await AnalyticsService.getPlatformOverview(filters);

    res.status(200).json({
      status: 'success',
      data: {
        analytics
      }
    });
  } catch (error) {
    logger.error('Get platform analytics error:', error);
    next(error);
  }
};
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Course enrollment trends
    const enrollmentTrends = await User.aggregate([
      { $unwind: '$enrolledCourses' },
      {
        $match: { 
          'enrolledCourses.enrollmentDate': { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$enrolledCourses.enrollmentDate' },
            month: { $month: '$enrolledCourses.enrollmentDate' },
            day: { $dayOfMonth: '$enrolledCourses.enrollmentDate' }
          },
          enrollments: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Top performing categories
    const topCategories = await Course.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: '$category',
          totalCourses: { $sum: 1 },
          totalEnrollments: { $sum: '$enrollmentCount' },
          averageRating: { $avg: '$rating' },
          totalRevenue: { $sum: '$price' }
        }
      },
      { $sort: { totalEnrollments: -1 } }
    ]);

    // Platform overview statistics
    const overview = {
      totalUsers: await User.countDocuments(),
      activeUsers: await User.countDocuments({ isActive: true }),
      totalCourses: await Course.countDocuments({ isActive: true }),
      totalInstructors: await User.countDocuments({ role: 'instructor', isActive: true }),
      totalRevenue: await Payment.aggregate([
        { $match: { status: 'completed' } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]).then(result => result[0]?.total || 0),
      totalEnrollments: await User.aggregate([
        { $unwind: '$enrolledCourses' },
        { $count: 'total' }
      ]).then(result => result[0]?.total || 0)
    };

    const analytics = {
      overview,
      userGrowth,
      revenueAnalytics,
      enrollmentTrends,
      topCategories,
      conversionRate: overview.totalEnrollments > 0 ? 
        Math.round((overview.totalEnrollments / overview.totalUsers) * 100) : 0
    };

    res.status(200).json({
      status: 'success',
      data: {
        period: `${period} days`,
        analytics
      }
    });
  } catch (error) {
    logger.error('Get platform analytics error:', error);
    next(error);
  }
};

// Get engagement metrics
const getEngagementMetrics = async (req, res, next) => {
  try {
    const { period = '7' } = req.query; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    // Daily active users
    const dailyActiveUsers = await User.aggregate([
      {
        $match: { 
          lastActive: { $gte: startDate },
          isActive: true
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$lastActive' },
            month: { $month: '$lastActive' },
            day: { $dayOfMonth: '$lastActive' }
          },
          activeUsers: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Session duration analytics (based on learning stats)
    const sessionAnalytics = await User.aggregate([
      { $match: { 'learningStats.totalStudyTime': { $gt: 0 } } },
      {
        $group: {
          _id: null,
          averageStudyTime: { $avg: '$learningStats.totalStudyTime' },
          totalStudyTime: { $sum: '$learningStats.totalStudyTime' },
          activeStudents: { $sum: 1 }
        }
      }
    ]);

    // Login streak distribution
    const streakDistribution = await User.aggregate([
      {
        $bucket: {
          groupBy: '$learningStats.loginStreak',
          boundaries: [0, 1, 7, 30, 100],
          default: '100+',
          output: {
            count: { $sum: 1 },
            users: { $push: '$_id' }
          }
        }
      }
    ]);

    res.status(200).json({
      status: 'success',
      data: {
        dailyActiveUsers,
        sessionAnalytics: sessionAnalytics[0] || {
          averageStudyTime: 0,
          totalStudyTime: 0,
          activeStudents: 0
        },
        streakDistribution
      }
    });
  } catch (error) {
    logger.error('Get engagement metrics error:', error);
    next(error);
  }
};

module.exports = {
  getUserLearningAnalytics,
  getCourseAnalytics,
  getInstructorAnalytics,
  getPlatformAnalytics,
  getEngagementMetrics
};
