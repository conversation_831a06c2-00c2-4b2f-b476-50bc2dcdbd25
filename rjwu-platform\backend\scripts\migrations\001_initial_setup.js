const mongoose = require('mongoose');
const logger = require('../../src/utils/logger');

/**
 * Migration: Initial Database Setup
 * Creates indexes and initial configuration
 */

async function up() {
  try {
    logger.info('Running migration: 001_initial_setup');
    
    const db = mongoose.connection.db;
    
    // Create indexes for User collection
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('users').createIndex({ phone: 1 }, { unique: true });
    await db.collection('users').createIndex({ role: 1 });
    await db.collection('users').createIndex({ isActive: 1 });
    await db.collection('users').createIndex({ createdAt: 1 });
    await db.collection('users').createIndex({ 'enrolledCourses.course': 1 });
    
    // Create indexes for Course collection
    await db.collection('courses').createIndex({ title: 1 });
    await db.collection('courses').createIndex({ instructor: 1 });
    await db.collection('courses').createIndex({ category: 1 });
    await db.collection('courses').createIndex({ level: 1 });
    await db.collection('courses').createIndex({ status: 1 });
    await db.collection('courses').createIndex({ isActive: 1 });
    await db.collection('courses').createIndex({ price: 1 });
    await db.collection('courses').createIndex({ rating: 1 });
    await db.collection('courses').createIndex({ enrollmentCount: 1 });
    await db.collection('courses').createIndex({ createdAt: 1 });
    await db.collection('courses').createIndex({ 
      title: 'text', 
      description: 'text',
      'settings.learningOutcomes': 'text'
    });
    
    // Create indexes for Batch collection
    await db.collection('batches').createIndex({ course: 1 });
    await db.collection('batches').createIndex({ instructor: 1 });
    await db.collection('batches').createIndex({ status: 1 });
    await db.collection('batches').createIndex({ startDate: 1 });
    await db.collection('batches').createIndex({ endDate: 1 });
    await db.collection('batches').createIndex({ 'students.student': 1 });
    
    // Create indexes for Payment collection
    await db.collection('payments').createIndex({ user: 1 });
    await db.collection('payments').createIndex({ course: 1 });
    await db.collection('payments').createIndex({ status: 1 });
    await db.collection('payments').createIndex({ gateway: 1 });
    await db.collection('payments').createIndex({ gatewayOrderId: 1 }, { unique: true });
    await db.collection('payments').createIndex({ gatewayPaymentId: 1 });
    await db.collection('payments').createIndex({ createdAt: 1 });
    
    // Create compound indexes for common queries
    await db.collection('courses').createIndex({ 
      category: 1, 
      level: 1, 
      status: 1 
    });
    
    await db.collection('users').createIndex({ 
      role: 1, 
      isActive: 1 
    });
    
    await db.collection('payments').createIndex({ 
      user: 1, 
      status: 1, 
      createdAt: -1 
    });
    
    logger.info('Migration 001_initial_setup completed successfully');
    
  } catch (error) {
    logger.error('Migration 001_initial_setup failed:', error);
    throw error;
  }
}

async function down() {
  try {
    logger.info('Rolling back migration: 001_initial_setup');
    
    const db = mongoose.connection.db;
    
    // Drop all indexes except _id (which cannot be dropped)
    const collections = ['users', 'courses', 'batches', 'payments'];
    
    for (const collectionName of collections) {
      try {
        const collection = db.collection(collectionName);
        const indexes = await collection.indexes();
        
        for (const index of indexes) {
          if (index.name !== '_id_') {
            await collection.dropIndex(index.name);
          }
        }
        
        logger.info(`Dropped indexes for ${collectionName} collection`);
      } catch (error) {
        // Collection might not exist, which is fine
        logger.warn(`Could not drop indexes for ${collectionName}:`, error.message);
      }
    }
    
    logger.info('Migration 001_initial_setup rollback completed');
    
  } catch (error) {
    logger.error('Migration 001_initial_setup rollback failed:', error);
    throw error;
  }
}

module.exports = {
  up,
  down,
  description: 'Initial database setup with indexes'
};
