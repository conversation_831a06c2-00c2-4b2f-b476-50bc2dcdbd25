/**
 * Shared Date and Time Utilities
 * Common date/time functions used across frontend and backend
 */

/**
 * Format date to readable string
 * @param {Date|string} date - Date to format
 * @param {string} format - Format type ('short', 'long', 'time', 'datetime')
 * @param {string} locale - Locale (default: 'en-IN')
 * @returns {string} - Formatted date string
 */
function formatDate(date, format = 'short', locale = 'en-IN') {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';

  const options = {
    short: { year: 'numeric', month: 'short', day: 'numeric' },
    long: { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' },
    time: { hour: '2-digit', minute: '2-digit', hour12: true },
    datetime: { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric', 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    },
    date: { year: 'numeric', month: '2-digit', day: '2-digit' }
  };

  return dateObj.toLocaleDateString(locale, options[format] || options.short);
}

/**
 * Get relative time (e.g., "2 hours ago", "in 3 days")
 * @param {Date|string} date - Date to compare
 * @param {Date|string} baseDate - Base date for comparison (default: now)
 * @returns {string} - Relative time string
 */
function getRelativeTime(date, baseDate = new Date()) {
  if (!date) return '';
  
  const dateObj = new Date(date);
  const baseObj = new Date(baseDate);
  
  if (isNaN(dateObj.getTime()) || isNaN(baseObj.getTime())) return '';

  const diffMs = dateObj.getTime() - baseObj.getTime();
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffWeek = Math.floor(diffDay / 7);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffDay / 365);

  const isPast = diffMs < 0;
  const abs = Math.abs;

  if (abs(diffSec) < 60) {
    return isPast ? 'just now' : 'in a moment';
  } else if (abs(diffMin) < 60) {
    const unit = abs(diffMin) === 1 ? 'minute' : 'minutes';
    return isPast ? `${abs(diffMin)} ${unit} ago` : `in ${abs(diffMin)} ${unit}`;
  } else if (abs(diffHour) < 24) {
    const unit = abs(diffHour) === 1 ? 'hour' : 'hours';
    return isPast ? `${abs(diffHour)} ${unit} ago` : `in ${abs(diffHour)} ${unit}`;
  } else if (abs(diffDay) < 7) {
    const unit = abs(diffDay) === 1 ? 'day' : 'days';
    return isPast ? `${abs(diffDay)} ${unit} ago` : `in ${abs(diffDay)} ${unit}`;
  } else if (abs(diffWeek) < 4) {
    const unit = abs(diffWeek) === 1 ? 'week' : 'weeks';
    return isPast ? `${abs(diffWeek)} ${unit} ago` : `in ${abs(diffWeek)} ${unit}`;
  } else if (abs(diffMonth) < 12) {
    const unit = abs(diffMonth) === 1 ? 'month' : 'months';
    return isPast ? `${abs(diffMonth)} ${unit} ago` : `in ${abs(diffMonth)} ${unit}`;
  } else {
    const unit = abs(diffYear) === 1 ? 'year' : 'years';
    return isPast ? `${abs(diffYear)} ${unit} ago` : `in ${abs(diffYear)} ${unit}`;
  }
}

/**
 * Check if date is today
 * @param {Date|string} date - Date to check
 * @returns {boolean} - True if date is today
 */
function isToday(date) {
  if (!date) return false;
  
  const dateObj = new Date(date);
  const today = new Date();
  
  return dateObj.toDateString() === today.toDateString();
}

/**
 * Check if date is yesterday
 * @param {Date|string} date - Date to check
 * @returns {boolean} - True if date is yesterday
 */
function isYesterday(date) {
  if (!date) return false;
  
  const dateObj = new Date(date);
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  
  return dateObj.toDateString() === yesterday.toDateString();
}

/**
 * Check if date is tomorrow
 * @param {Date|string} date - Date to check
 * @returns {boolean} - True if date is tomorrow
 */
function isTomorrow(date) {
  if (!date) return false;
  
  const dateObj = new Date(date);
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  return dateObj.toDateString() === tomorrow.toDateString();
}

/**
 * Get start of day
 * @param {Date|string} date - Date (default: today)
 * @returns {Date} - Start of day
 */
function getStartOfDay(date = new Date()) {
  const dateObj = new Date(date);
  dateObj.setHours(0, 0, 0, 0);
  return dateObj;
}

/**
 * Get end of day
 * @param {Date|string} date - Date (default: today)
 * @returns {Date} - End of day
 */
function getEndOfDay(date = new Date()) {
  const dateObj = new Date(date);
  dateObj.setHours(23, 59, 59, 999);
  return dateObj;
}

/**
 * Add days to date
 * @param {Date|string} date - Base date
 * @param {number} days - Number of days to add
 * @returns {Date} - New date
 */
function addDays(date, days) {
  const dateObj = new Date(date);
  dateObj.setDate(dateObj.getDate() + days);
  return dateObj;
}

/**
 * Add hours to date
 * @param {Date|string} date - Base date
 * @param {number} hours - Number of hours to add
 * @returns {Date} - New date
 */
function addHours(date, hours) {
  const dateObj = new Date(date);
  dateObj.setHours(dateObj.getHours() + hours);
  return dateObj;
}

/**
 * Get difference in days between two dates
 * @param {Date|string} date1 - First date
 * @param {Date|string} date2 - Second date
 * @returns {number} - Difference in days
 */
function getDaysDifference(date1, date2) {
  const d1 = new Date(date1);
  const d2 = new Date(date2);
  const diffTime = Math.abs(d2.getTime() - d1.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Format duration in minutes to human readable format
 * @param {number} minutes - Duration in minutes
 * @returns {string} - Formatted duration
 */
function formatDuration(minutes) {
  if (!minutes || minutes < 0) return '0 min';
  
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  if (hours === 0) {
    return `${mins} min`;
  } else if (mins === 0) {
    return `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
  } else {
    return `${hours}h ${mins}m`;
  }
}

/**
 * Parse time string (HH:MM) to minutes
 * @param {string} timeString - Time in HH:MM format
 * @returns {number} - Minutes since midnight
 */
function parseTimeToMinutes(timeString) {
  if (!timeString || typeof timeString !== 'string') return 0;
  
  const [hours, minutes] = timeString.split(':').map(Number);
  if (isNaN(hours) || isNaN(minutes)) return 0;
  
  return hours * 60 + minutes;
}

/**
 * Format minutes to time string (HH:MM)
 * @param {number} minutes - Minutes since midnight
 * @returns {string} - Time in HH:MM format
 */
function formatMinutesToTime(minutes) {
  if (!minutes || minutes < 0) return '00:00';
  
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

/**
 * Check if time is within business hours
 * @param {Date|string} date - Date to check
 * @param {object} businessHours - Business hours config
 * @returns {boolean} - True if within business hours
 */
function isWithinBusinessHours(date, businessHours = { start: '09:00', end: '18:00' }) {
  const dateObj = new Date(date);
  const hours = dateObj.getHours();
  const minutes = dateObj.getMinutes();
  const currentMinutes = hours * 60 + minutes;
  
  const startMinutes = parseTimeToMinutes(businessHours.start);
  const endMinutes = parseTimeToMinutes(businessHours.end);
  
  return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
}

/**
 * Get timezone offset in hours
 * @param {string} timezone - Timezone (default: local)
 * @returns {number} - Offset in hours
 */
function getTimezoneOffset(timezone = null) {
  if (timezone) {
    // For specific timezone (requires Intl support)
    try {
      const date = new Date();
      const utc = date.getTime() + (date.getTimezoneOffset() * 60000);
      const targetTime = new Date(utc + (getTimezoneOffsetMinutes(timezone) * 60000));
      return targetTime.getTimezoneOffset() / -60;
    } catch {
      return 0;
    }
  }
  
  return new Date().getTimezoneOffset() / -60;
}

/**
 * Convert date to ISO string for API
 * @param {Date|string} date - Date to convert
 * @returns {string} - ISO string
 */
function toISOString(date) {
  if (!date) return '';
  const dateObj = new Date(date);
  return isNaN(dateObj.getTime()) ? '' : dateObj.toISOString();
}

/**
 * Parse ISO string to local date
 * @param {string} isoString - ISO date string
 * @returns {Date|null} - Parsed date or null
 */
function fromISOString(isoString) {
  if (!isoString) return null;
  const date = new Date(isoString);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * Get academic year from date
 * @param {Date|string} date - Date to check
 * @returns {string} - Academic year (e.g., "2023-24")
 */
function getAcademicYear(date = new Date()) {
  const dateObj = new Date(date);
  const year = dateObj.getFullYear();
  const month = dateObj.getMonth(); // 0-based
  
  // Academic year starts in April (month 3)
  if (month >= 3) {
    return `${year}-${(year + 1).toString().slice(-2)}`;
  } else {
    return `${year - 1}-${year.toString().slice(-2)}`;
  }
}

/**
 * Common date formats
 */
const DATE_FORMATS = {
  SHORT: 'short',
  LONG: 'long',
  TIME: 'time',
  DATETIME: 'datetime',
  DATE: 'date'
};

/**
 * Common time constants
 */
const TIME_CONSTANTS = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
  MONTH: 30 * 24 * 60 * 60 * 1000,
  YEAR: 365 * 24 * 60 * 60 * 1000
};

// Export for both CommonJS (Node.js) and ES modules (frontend)
const dateTimeUtils = {
  formatDate,
  getRelativeTime,
  isToday,
  isYesterday,
  isTomorrow,
  getStartOfDay,
  getEndOfDay,
  addDays,
  addHours,
  getDaysDifference,
  formatDuration,
  parseTimeToMinutes,
  formatMinutesToTime,
  isWithinBusinessHours,
  getTimezoneOffset,
  toISOString,
  fromISOString,
  getAcademicYear,
  DATE_FORMATS,
  TIME_CONSTANTS
};

// CommonJS export (Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = dateTimeUtils;
}

// ES module export (frontend)
if (typeof window !== 'undefined') {
  window.DateTimeUtils = dateTimeUtils;
}
