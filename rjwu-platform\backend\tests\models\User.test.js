const User = require('../../src/models/User');
const bcrypt = require('bcryptjs');

describe('User Model', () => {
  describe('User Creation', () => {
    test('should create a valid user', async () => {
      const userData = global.testUtils.createTestUser();
      const user = new User(userData);
      const savedUser = await user.save();

      expect(savedUser._id).toBeDefined();
      expect(savedUser.email).toBe(userData.email);
      expect(savedUser.firstName).toBe(userData.firstName);
      expect(savedUser.lastName).toBe(userData.lastName);
      expect(savedUser.role).toBe('student');
      expect(savedUser.isActive).toBe(true);
    });

    test('should hash password before saving', async () => {
      const userData = global.testUtils.createTestUser();
      const user = new User(userData);
      const savedUser = await user.save();

      expect(savedUser.password).not.toBe(userData.password);
      expect(savedUser.password).toMatch(/^\$2[ayb]\$.{56}$/); // bcrypt hash pattern
    });

    test('should require required fields', async () => {
      const user = new User({});
      
      await expect(user.save()).rejects.toThrow();
    });

    test('should validate email format', async () => {
      const userData = global.testUtils.createTestUser({
        email: 'invalid-email'
      });
      const user = new User(userData);

      await expect(user.save()).rejects.toThrow();
    });

    test('should validate phone number format', async () => {
      const userData = global.testUtils.createTestUser({
        phone: '123' // Invalid Indian phone number
      });
      const user = new User(userData);

      await expect(user.save()).rejects.toThrow();
    });

    test('should enforce unique email', async () => {
      const userData = global.testUtils.createTestUser();
      
      // Create first user
      const user1 = new User(userData);
      await user1.save();

      // Try to create second user with same email
      const user2 = new User(userData);
      await expect(user2.save()).rejects.toThrow();
    });
  });

  describe('User Methods', () => {
    let user;

    beforeEach(async () => {
      const userData = global.testUtils.createTestUser();
      user = new User(userData);
      await user.save();
    });

    test('should compare password correctly', async () => {
      const isMatch = await user.comparePassword('testpassword123');
      expect(isMatch).toBe(true);

      const isNotMatch = await user.comparePassword('wrongpassword');
      expect(isNotMatch).toBe(false);
    });

    test('should generate full name', () => {
      expect(user.fullName).toBe('Test User');
    });

    test('should update last active timestamp', async () => {
      const originalLastActive = user.lastActive;
      await global.testUtils.wait(10); // Wait 10ms
      
      user.updateLastActive();
      await user.save();

      expect(user.lastActive.getTime()).toBeGreaterThan(originalLastActive.getTime());
    });

    test('should check if user is locked', () => {
      expect(user.isLocked).toBe(false);

      user.lockUntil = new Date(Date.now() + 60000); // Lock for 1 minute
      expect(user.isLocked).toBe(true);

      user.lockUntil = new Date(Date.now() - 60000); // Lock expired
      expect(user.isLocked).toBe(false);
    });

    test('should increment login attempts', async () => {
      expect(user.loginAttempts).toBe(0);

      await user.incLoginAttempts();
      expect(user.loginAttempts).toBe(1);

      await user.incLoginAttempts();
      expect(user.loginAttempts).toBe(2);
    });

    test('should reset login attempts on successful login', async () => {
      user.loginAttempts = 3;
      await user.save();

      await user.resetLoginAttempts();
      expect(user.loginAttempts).toBe(0);
      expect(user.lockUntil).toBeUndefined();
    });
  });

  describe('User Roles and Permissions', () => {
    test('should create student user by default', async () => {
      const userData = global.testUtils.createTestUser();
      delete userData.role; // Remove role to test default
      
      const user = new User(userData);
      await user.save();

      expect(user.role).toBe('student');
    });

    test('should create instructor user', async () => {
      const userData = global.testUtils.createTestUser({
        role: 'instructor'
      });
      const user = new User(userData);
      await user.save();

      expect(user.role).toBe('instructor');
    });

    test('should create admin user', async () => {
      const userData = global.testUtils.createTestUser({
        role: 'admin'
      });
      const user = new User(userData);
      await user.save();

      expect(user.role).toBe('admin');
    });

    test('should not allow invalid roles', async () => {
      const userData = global.testUtils.createTestUser({
        role: 'invalid-role'
      });
      const user = new User(userData);

      await expect(user.save()).rejects.toThrow();
    });
  });

  describe('User Profile', () => {
    test('should store user address', async () => {
      const userData = global.testUtils.createTestUser({
        address: {
          street: '123 Test Street',
          city: 'Jaipur',
          state: 'Rajasthan',
          pincode: '302001',
          country: 'India'
        }
      });
      const user = new User(userData);
      await user.save();

      expect(user.address.city).toBe('Jaipur');
      expect(user.address.state).toBe('Rajasthan');
      expect(user.address.country).toBe('India');
    });

    test('should store education information', async () => {
      const userData = global.testUtils.createTestUser({
        education: {
          highestQualification: 'Bachelor\'s Degree',
          fieldOfStudy: 'Computer Science',
          institution: 'Test University',
          graduationYear: 2020
        }
      });
      const user = new User(userData);
      await user.save();

      expect(user.education.highestQualification).toBe('Bachelor\'s Degree');
      expect(user.education.fieldOfStudy).toBe('Computer Science');
    });

    test('should store instructor profile for instructors', async () => {
      const userData = global.testUtils.createTestUser({
        role: 'instructor',
        instructorProfile: {
          bio: 'Experienced instructor',
          experience: 5,
          specializations: ['JavaScript', 'React'],
          rating: 4.5,
          totalStudents: 100
        }
      });
      const user = new User(userData);
      await user.save();

      expect(user.instructorProfile.bio).toBe('Experienced instructor');
      expect(user.instructorProfile.specializations).toContain('JavaScript');
      expect(user.instructorProfile.rating).toBe(4.5);
    });
  });

  describe('User Learning Stats', () => {
    test('should initialize learning stats', async () => {
      const userData = global.testUtils.createTestUser();
      const user = new User(userData);
      await user.save();

      expect(user.learningStats.totalStudyTime).toBe(0);
      expect(user.learningStats.loginStreak).toBe(0);
      expect(user.learningStats.totalTestsAttempted).toBe(0);
      expect(user.learningStats.averageScore).toBe(0);
    });

    test('should update learning stats', async () => {
      const userData = global.testUtils.createTestUser();
      const user = new User(userData);
      await user.save();

      user.learningStats.totalStudyTime = 120; // 2 hours
      user.learningStats.loginStreak = 7;
      user.learningStats.totalTestsAttempted = 5;
      user.learningStats.averageScore = 85;

      await user.save();

      expect(user.learningStats.totalStudyTime).toBe(120);
      expect(user.learningStats.loginStreak).toBe(7);
      expect(user.learningStats.averageScore).toBe(85);
    });
  });

  describe('User Enrollment', () => {
    test('should enroll user in course', async () => {
      const userData = global.testUtils.createTestUser();
      const user = new User(userData);
      await user.save();

      const courseId = '507f1f77bcf86cd799439011'; // Mock ObjectId
      user.enrolledCourses.push({
        course: courseId,
        enrollmentDate: new Date(),
        progress: 0,
        status: 'active'
      });

      await user.save();

      expect(user.enrolledCourses).toHaveLength(1);
      expect(user.enrolledCourses[0].course.toString()).toBe(courseId);
      expect(user.enrolledCourses[0].status).toBe('active');
    });
  });
});
