{"name": "rjwu-frontend", "version": "1.0.0", "description": "RJWU EduTech Platform Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:components": "jest tests/components", "test:pages": "jest tests/pages", "test:hooks": "jest tests/hooks", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "axios": "^1.6.2", "chart.js": "^4.4.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "js-cookie": "^3.0.5", "lucide-react": "^0.295.0", "next": "^14.0.4", "next-i18next": "^15.2.0", "next-pwa": "^5.6.0", "next-seo": "^6.4.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-helmet": "^6.1.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-intersection-observer": "^9.5.3", "react-markdown": "^9.0.1", "react-player": "^2.13.0", "react-query": "^3.39.3", "react-share": "^4.4.1", "react-syntax-highlighter": "^15.5.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "remark-gfm": "^4.0.0", "sharp": "^0.33.1", "socket.io-client": "^4.7.4", "tailwind-merge": "^3.3.1", "zustand": "^4.4.7"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.4", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-helmet": "^6.1.11", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}