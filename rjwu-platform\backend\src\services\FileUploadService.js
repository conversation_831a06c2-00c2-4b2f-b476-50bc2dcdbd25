const cloudinary = require('cloudinary').v2;
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const BaseService = require('./BaseService');
const config = require('../config/config');

/**
 * File Upload Service
 * Handles all file upload operations including images, videos, and documents
 */
class FileUploadService extends BaseService {
  constructor() {
    super('FileUploadService');
    this.initializeCloudinary();
    this.initializeMulter();
  }

  /**
   * Initialize Cloudinary
   */
  initializeCloudinary() {
    try {
      cloudinary.config({
        cloud_name: config.CLOUDINARY_CLOUD_NAME,
        api_key: config.CLOUDINARY_API_KEY,
        api_secret: config.CLOUDINARY_API_SECRET
      });

      this.log('info', 'Cloudinary initialized successfully');
    } catch (error) {
      this.handleError(error, 'Cloudinary initialization');
    }
  }

  /**
   * Initialize Multer for file handling
   */
  initializeMulter() {
    try {
      // Configure multer for temporary file storage
      this.upload = multer({
        dest: 'temp/',
        limits: {
          fileSize: config.MAX_FILE_SIZE || 50 * 1024 * 1024 // 50MB default
        },
        fileFilter: (req, file, cb) => {
          this.validateFile(file, cb);
        }
      });

      this.log('info', 'Multer initialized successfully');
    } catch (error) {
      this.handleError(error, 'Multer initialization');
    }
  }

  /**
   * Validate uploaded file
   */
  validateFile(file, callback) {
    try {
      const allowedTypes = [
        ...config.ALLOWED_IMAGE_TYPES,
        ...config.ALLOWED_VIDEO_TYPES,
        ...config.ALLOWED_DOCUMENT_TYPES
      ];

      if (!allowedTypes.includes(file.mimetype)) {
        return callback(new Error(`File type ${file.mimetype} is not allowed`));
      }

      callback(null, true);
    } catch (error) {
      callback(error);
    }
  }

  /**
   * Upload file to Cloudinary
   * @param {Object} file - File object from multer
   * @param {Object} options - Upload options
   * @param {string} options.folder - Cloudinary folder
   * @param {string} options.resourceType - Resource type (image, video, raw)
   * @param {Object} options.transformation - Cloudinary transformations
   */
  async uploadToCloudinary(file, options = {}) {
    try {
      const {
        folder = 'rjwu',
        resourceType = 'auto',
        transformation = {}
      } = options;

      const uploadOptions = {
        folder,
        resource_type: resourceType,
        use_filename: true,
        unique_filename: true,
        ...transformation
      };

      const result = await cloudinary.uploader.upload(file.path, uploadOptions);

      // Clean up temporary file
      await this.cleanupTempFile(file.path);

      this.log('info', 'File uploaded to Cloudinary successfully', {
        publicId: result.public_id,
        url: result.secure_url,
        resourceType: result.resource_type
      });

      return {
        publicId: result.public_id,
        url: result.secure_url,
        secureUrl: result.secure_url,
        format: result.format,
        resourceType: result.resource_type,
        bytes: result.bytes,
        width: result.width,
        height: result.height,
        createdAt: result.created_at
      };
    } catch (error) {
      // Clean up temporary file on error
      if (file && file.path) {
        await this.cleanupTempFile(file.path);
      }
      this.handleError(error, 'Upload to Cloudinary');
    }
  }

  /**
   * Upload profile avatar
   * @param {Object} file - File object
   * @param {string} userId - User ID
   */
  async uploadAvatar(file, userId) {
    try {
      const result = await this.uploadToCloudinary(file, {
        folder: 'rjwu/avatars',
        resourceType: 'image',
        transformation: {
          width: 300,
          height: 300,
          crop: 'fill',
          gravity: 'face',
          quality: 'auto',
          format: 'jpg'
        }
      });

      return this.formatResponse(result, 'Avatar uploaded successfully');
    } catch (error) {
      this.handleError(error, 'Upload avatar', { userId });
    }
  }

  /**
   * Upload course thumbnail
   * @param {Object} file - File object
   * @param {string} courseId - Course ID
   */
  async uploadCourseThumbnail(file, courseId) {
    try {
      const result = await this.uploadToCloudinary(file, {
        folder: 'rjwu/courses/thumbnails',
        resourceType: 'image',
        transformation: {
          width: 800,
          height: 450,
          crop: 'fill',
          quality: 'auto',
          format: 'jpg'
        }
      });

      return this.formatResponse(result, 'Course thumbnail uploaded successfully');
    } catch (error) {
      this.handleError(error, 'Upload course thumbnail', { courseId });
    }
  }

  /**
   * Upload course video
   * @param {Object} file - File object
   * @param {string} courseId - Course ID
   * @param {string} chapterId - Chapter ID
   */
  async uploadCourseVideo(file, courseId, chapterId) {
    try {
      const result = await this.uploadToCloudinary(file, {
        folder: `rjwu/courses/${courseId}/videos`,
        resourceType: 'video',
        transformation: {
          quality: 'auto',
          format: 'mp4'
        }
      });

      return this.formatResponse(result, 'Course video uploaded successfully');
    } catch (error) {
      this.handleError(error, 'Upload course video', { courseId, chapterId });
    }
  }

  /**
   * Upload document/PDF
   * @param {Object} file - File object
   * @param {string} folder - Target folder
   */
  async uploadDocument(file, folder = 'documents') {
    try {
      const result = await this.uploadToCloudinary(file, {
        folder: `rjwu/${folder}`,
        resourceType: 'raw'
      });

      return this.formatResponse(result, 'Document uploaded successfully');
    } catch (error) {
      this.handleError(error, 'Upload document', { folder });
    }
  }

  /**
   * Upload multiple files
   * @param {Array} files - Array of file objects
   * @param {Object} options - Upload options
   */
  async uploadMultiple(files, options = {}) {
    try {
      const results = [];
      const errors = [];

      for (const file of files) {
        try {
          const result = await this.uploadToCloudinary(file, options);
          results.push(result);
        } catch (error) {
          errors.push({
            filename: file.originalname,
            error: error.message
          });
        }
      }

      return this.formatResponse({
        successful: results,
        failed: errors,
        totalUploaded: results.length,
        totalFailed: errors.length
      }, 'Multiple files upload completed');
    } catch (error) {
      this.handleError(error, 'Upload multiple files');
    }
  }

  /**
   * Delete file from Cloudinary
   * @param {string} publicId - Cloudinary public ID
   * @param {string} resourceType - Resource type (image, video, raw)
   */
  async deleteFile(publicId, resourceType = 'image') {
    try {
      const result = await cloudinary.uploader.destroy(publicId, {
        resource_type: resourceType
      });

      this.log('info', 'File deleted from Cloudinary', {
        publicId,
        result: result.result
      });

      return this.formatResponse(result, 'File deleted successfully');
    } catch (error) {
      this.handleError(error, 'Delete file', { publicId, resourceType });
    }
  }

  /**
   * Generate signed upload URL for direct uploads
   * @param {Object} options - Upload options
   */
  generateSignedUploadUrl(options = {}) {
    try {
      const timestamp = Math.round(new Date().getTime() / 1000);
      const params = {
        timestamp,
        ...options
      };

      const signature = cloudinary.utils.api_sign_request(params, config.CLOUDINARY_API_SECRET);

      return this.formatResponse({
        url: `https://api.cloudinary.com/v1_1/${config.CLOUDINARY_CLOUD_NAME}/upload`,
        params: {
          ...params,
          signature,
          api_key: config.CLOUDINARY_API_KEY
        }
      }, 'Signed upload URL generated');
    } catch (error) {
      this.handleError(error, 'Generate signed upload URL');
    }
  }

  /**
   * Get file information
   * @param {string} publicId - Cloudinary public ID
   * @param {string} resourceType - Resource type
   */
  async getFileInfo(publicId, resourceType = 'image') {
    try {
      const result = await cloudinary.api.resource(publicId, {
        resource_type: resourceType
      });

      return this.formatResponse(result, 'File information retrieved');
    } catch (error) {
      this.handleError(error, 'Get file info', { publicId, resourceType });
    }
  }

  /**
   * Transform image
   * @param {string} publicId - Cloudinary public ID
   * @param {Object} transformations - Transformation options
   */
  transformImage(publicId, transformations = {}) {
    try {
      const url = cloudinary.url(publicId, {
        ...transformations,
        secure: true
      });

      return this.formatResponse({ url }, 'Image transformation URL generated');
    } catch (error) {
      this.handleError(error, 'Transform image', { publicId, transformations });
    }
  }

  /**
   * Clean up temporary file
   * @param {string} filePath - Path to temporary file
   */
  async cleanupTempFile(filePath) {
    try {
      await fs.unlink(filePath);
      this.log('debug', 'Temporary file cleaned up', { filePath });
    } catch (error) {
      this.log('warn', 'Failed to clean up temporary file', {
        filePath,
        error: error.message
      });
    }
  }

  /**
   * Get upload middleware for specific file types
   * @param {string} fieldName - Form field name
   * @param {string} fileType - File type (single, multiple, avatar, etc.)
   */
  getUploadMiddleware(fieldName, fileType = 'single') {
    switch (fileType) {
      case 'single':
        return this.upload.single(fieldName);
      case 'multiple':
        return this.upload.array(fieldName, 10); // Max 10 files
      case 'avatar':
        return this.upload.single(fieldName);
      case 'fields':
        return this.upload.fields([
          { name: 'thumbnail', maxCount: 1 },
          { name: 'video', maxCount: 1 },
          { name: 'documents', maxCount: 5 }
        ]);
      default:
        return this.upload.single(fieldName);
    }
  }

  /**
   * Validate file size and type
   * @param {Object} file - File object
   * @param {Object} constraints - Validation constraints
   */
  validateFileConstraints(file, constraints = {}) {
    const {
      maxSize = config.MAX_FILE_SIZE,
      allowedTypes = [...config.ALLOWED_IMAGE_TYPES, ...config.ALLOWED_VIDEO_TYPES, ...config.ALLOWED_DOCUMENT_TYPES]
    } = constraints;

    if (file.size > maxSize) {
      throw new Error(`File size exceeds maximum allowed size of ${maxSize} bytes`);
    }

    if (!allowedTypes.includes(file.mimetype)) {
      throw new Error(`File type ${file.mimetype} is not allowed`);
    }

    return true;
  }
}

module.exports = new FileUploadService();
