/**
 * Shared String Utilities
 * Common string manipulation functions used across frontend and backend
 */

/**
 * Convert string to slug (URL-friendly)
 * @param {string} str - String to convert
 * @returns {string} - Slugified string
 */
function slugify(str) {
  if (!str || typeof str !== 'string') return '';
  
  return str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Capitalize first letter of string
 * @param {string} str - String to capitalize
 * @returns {string} - Capitalized string
 */
function capitalize(str) {
  if (!str || typeof str !== 'string') return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Convert string to title case
 * @param {string} str - String to convert
 * @returns {string} - Title case string
 */
function toTitleCase(str) {
  if (!str || typeof str !== 'string') return '';
  
  return str
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Convert camelCase to kebab-case
 * @param {string} str - CamelCase string
 * @returns {string} - kebab-case string
 */
function camelToKebab(str) {
  if (!str || typeof str !== 'string') return '';
  
  return str
    .replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2')
    .toLowerCase();
}

/**
 * Convert kebab-case to camelCase
 * @param {string} str - kebab-case string
 * @returns {string} - camelCase string
 */
function kebabToCamel(str) {
  if (!str || typeof str !== 'string') return '';
  
  return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
}

/**
 * Truncate string with ellipsis
 * @param {string} str - String to truncate
 * @param {number} length - Maximum length
 * @param {string} suffix - Suffix to add (default: '...')
 * @returns {string} - Truncated string
 */
function truncate(str, length = 100, suffix = '...') {
  if (!str || typeof str !== 'string') return '';
  if (str.length <= length) return str;
  
  return str.slice(0, length - suffix.length).trim() + suffix;
}

/**
 * Truncate string at word boundary
 * @param {string} str - String to truncate
 * @param {number} length - Maximum length
 * @param {string} suffix - Suffix to add (default: '...')
 * @returns {string} - Truncated string
 */
function truncateWords(str, length = 100, suffix = '...') {
  if (!str || typeof str !== 'string') return '';
  if (str.length <= length) return str;
  
  const truncated = str.slice(0, length - suffix.length);
  const lastSpace = truncated.lastIndexOf(' ');
  
  if (lastSpace > 0) {
    return truncated.slice(0, lastSpace).trim() + suffix;
  }
  
  return truncated.trim() + suffix;
}

/**
 * Extract initials from name
 * @param {string} name - Full name
 * @param {number} maxInitials - Maximum number of initials (default: 2)
 * @returns {string} - Initials
 */
function getInitials(name, maxInitials = 2) {
  if (!name || typeof name !== 'string') return '';
  
  return name
    .split(' ')
    .filter(word => word.length > 0)
    .slice(0, maxInitials)
    .map(word => word.charAt(0).toUpperCase())
    .join('');
}

/**
 * Generate random string
 * @param {number} length - Length of string
 * @param {string} charset - Character set to use
 * @returns {string} - Random string
 */
function generateRandomString(length = 8, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
}

/**
 * Generate unique ID
 * @param {string} prefix - Prefix for ID
 * @returns {string} - Unique ID
 */
function generateUniqueId(prefix = '') {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return prefix ? `${prefix}_${timestamp}_${random}` : `${timestamp}_${random}`;
}

/**
 * Clean and normalize text
 * @param {string} text - Text to clean
 * @returns {string} - Cleaned text
 */
function cleanText(text) {
  if (!text || typeof text !== 'string') return '';
  
  return text
    .trim()
    .replace(/\s+/g, ' ') // Normalize whitespace
    .replace(/[^\w\s.,!?;:()\-'"]/g, '') // Remove special characters except common punctuation
    .replace(/\n+/g, '\n'); // Normalize line breaks
}

/**
 * Extract hashtags from text
 * @param {string} text - Text to extract from
 * @returns {string[]} - Array of hashtags
 */
function extractHashtags(text) {
  if (!text || typeof text !== 'string') return [];
  
  const hashtags = text.match(/#[a-zA-Z0-9_]+/g);
  return hashtags ? hashtags.map(tag => tag.slice(1)) : [];
}

/**
 * Extract mentions from text
 * @param {string} text - Text to extract from
 * @returns {string[]} - Array of mentions
 */
function extractMentions(text) {
  if (!text || typeof text !== 'string') return [];
  
  const mentions = text.match(/@[a-zA-Z0-9_]+/g);
  return mentions ? mentions.map(mention => mention.slice(1)) : [];
}

/**
 * Highlight search terms in text
 * @param {string} text - Text to highlight
 * @param {string} searchTerm - Term to highlight
 * @param {string} highlightClass - CSS class for highlighting
 * @returns {string} - Text with highlighted terms
 */
function highlightSearchTerms(text, searchTerm, highlightClass = 'highlight') {
  if (!text || !searchTerm || typeof text !== 'string' || typeof searchTerm !== 'string') {
    return text || '';
  }
  
  const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, `<span class="${highlightClass}">$1</span>`);
}

/**
 * Count words in text
 * @param {string} text - Text to count
 * @returns {number} - Word count
 */
function countWords(text) {
  if (!text || typeof text !== 'string') return 0;
  
  return text
    .trim()
    .split(/\s+/)
    .filter(word => word.length > 0)
    .length;
}

/**
 * Estimate reading time
 * @param {string} text - Text to analyze
 * @param {number} wordsPerMinute - Reading speed (default: 200)
 * @returns {number} - Estimated reading time in minutes
 */
function estimateReadingTime(text, wordsPerMinute = 200) {
  const wordCount = countWords(text);
  return Math.ceil(wordCount / wordsPerMinute);
}

/**
 * Format file size
 * @param {number} bytes - File size in bytes
 * @param {number} decimals - Number of decimal places
 * @returns {string} - Formatted file size
 */
function formatFileSize(bytes, decimals = 2) {
  if (!bytes || bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Parse query string to object
 * @param {string} queryString - Query string
 * @returns {object} - Parsed object
 */
function parseQueryString(queryString) {
  if (!queryString || typeof queryString !== 'string') return {};
  
  const params = {};
  const pairs = queryString.replace(/^\?/, '').split('&');
  
  pairs.forEach(pair => {
    const [key, value] = pair.split('=');
    if (key) {
      params[decodeURIComponent(key)] = value ? decodeURIComponent(value) : '';
    }
  });
  
  return params;
}

/**
 * Convert object to query string
 * @param {object} obj - Object to convert
 * @returns {string} - Query string
 */
function objectToQueryString(obj) {
  if (!obj || typeof obj !== 'object') return '';
  
  const pairs = [];
  
  Object.keys(obj).forEach(key => {
    const value = obj[key];
    if (value !== null && value !== undefined && value !== '') {
      pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
    }
  });
  
  return pairs.length > 0 ? `?${pairs.join('&')}` : '';
}

/**
 * Mask sensitive information
 * @param {string} str - String to mask
 * @param {number} visibleChars - Number of visible characters at start/end
 * @param {string} maskChar - Character to use for masking
 * @returns {string} - Masked string
 */
function maskString(str, visibleChars = 2, maskChar = '*') {
  if (!str || typeof str !== 'string') return '';
  if (str.length <= visibleChars * 2) return str;
  
  const start = str.slice(0, visibleChars);
  const end = str.slice(-visibleChars);
  const middle = maskChar.repeat(str.length - visibleChars * 2);
  
  return start + middle + end;
}

/**
 * Check if string contains only digits
 * @param {string} str - String to check
 * @returns {boolean} - True if only digits
 */
function isNumeric(str) {
  if (!str || typeof str !== 'string') return false;
  return /^\d+$/.test(str);
}

/**
 * Check if string is valid JSON
 * @param {string} str - String to check
 * @returns {boolean} - True if valid JSON
 */
function isValidJSON(str) {
  if (!str || typeof str !== 'string') return false;
  
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
}

// Export for both CommonJS (Node.js) and ES modules (frontend)
const stringUtils = {
  slugify,
  capitalize,
  toTitleCase,
  camelToKebab,
  kebabToCamel,
  truncate,
  truncateWords,
  getInitials,
  generateRandomString,
  generateUniqueId,
  cleanText,
  extractHashtags,
  extractMentions,
  highlightSearchTerms,
  countWords,
  estimateReadingTime,
  formatFileSize,
  parseQueryString,
  objectToQueryString,
  maskString,
  isNumeric,
  isValidJSON
};

// CommonJS export (Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = stringUtils;
}

// ES module export (frontend)
if (typeof window !== 'undefined') {
  window.StringUtils = stringUtils;
}
