const request = require('supertest');
const app = require('../../src/app');
const User = require('../../src/models/User');
const jwt = require('jsonwebtoken');

describe('Auth Controller', () => {
  describe('POST /api/auth/register', () => {
    test('should register a new user successfully', async () => {
      const userData = global.testUtils.createTestUser();

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user.firstName).toBe(userData.firstName);
      expect(response.body.data.token).toBeDefined();

      // Verify user was created in database
      const user = await User.findOne({ email: userData.email });
      expect(user).toBeTruthy();
      expect(user.password).not.toBe(userData.password); // Should be hashed
    });

    test('should not register user with existing email', async () => {
      const userData = global.testUtils.createTestUser();

      // Create user first
      const user = new User(userData);
      await user.save();

      // Try to register with same email
      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('email');
    });

    test('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          firstName: 'Test'
          // Missing required fields
        })
        .expect(400);

      expect(response.body.status).toBe('error');
    });

    test('should validate email format', async () => {
      const userData = global.testUtils.createTestUser({
        email: 'invalid-email'
      });

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.status).toBe('error');
    });

    test('should validate phone number format', async () => {
      const userData = global.testUtils.createTestUser({
        phone: '123' // Invalid phone number
      });

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.status).toBe('error');
    });

    test('should validate password strength', async () => {
      const userData = global.testUtils.createTestUser({
        password: '123' // Weak password
      });

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.status).toBe('error');
    });
  });

  describe('POST /api/auth/login', () => {
    let user;

    beforeEach(async () => {
      const userData = global.testUtils.createTestUser();
      user = new User(userData);
      await user.save();
    });

    test('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: user.email,
          password: 'testpassword123'
        })
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.user.email).toBe(user.email);
      expect(response.body.data.token).toBeDefined();

      // Verify token is valid
      const decoded = jwt.verify(response.body.data.token, process.env.JWT_SECRET);
      expect(decoded.id).toBe(user._id.toString());
    });

    test('should not login with invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'testpassword123'
        })
        .expect(401);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('Invalid');
    });

    test('should not login with invalid password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: user.email,
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('Invalid');
    });

    test('should not login inactive user', async () => {
      user.isActive = false;
      await user.save();

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: user.email,
          password: 'testpassword123'
        })
        .expect(401);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('inactive');
    });

    test('should handle account lockout', async () => {
      // Simulate multiple failed login attempts
      for (let i = 0; i < 5; i++) {
        await request(app)
          .post('/api/auth/login')
          .send({
            email: user.email,
            password: 'wrongpassword'
          });
      }

      // Account should be locked now
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: user.email,
          password: 'testpassword123'
        })
        .expect(423);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('locked');
    });
  });

  describe('POST /api/auth/forgot-password', () => {
    let user;

    beforeEach(async () => {
      const userData = global.testUtils.createTestUser();
      user = new User(userData);
      await user.save();
    });

    test('should send password reset email for valid email', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({
          email: user.email
        })
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toContain('reset');

      // Verify reset token was set
      const updatedUser = await User.findById(user._id);
      expect(updatedUser.passwordResetToken).toBeDefined();
      expect(updatedUser.passwordResetExpires).toBeDefined();
    });

    test('should handle non-existent email gracefully', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({
          email: '<EMAIL>'
        })
        .expect(200);

      // Should still return success for security reasons
      expect(response.body.status).toBe('success');
    });

    test('should validate email format', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({
          email: 'invalid-email'
        })
        .expect(400);

      expect(response.body.status).toBe('error');
    });
  });

  describe('POST /api/auth/reset-password', () => {
    let user;
    let resetToken;

    beforeEach(async () => {
      const userData = global.testUtils.createTestUser();
      user = new User(userData);
      
      // Set reset token
      resetToken = 'test-reset-token';
      user.passwordResetToken = resetToken;
      user.passwordResetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
      
      await user.save();
    });

    test('should reset password with valid token', async () => {
      const newPassword = 'newpassword123';

      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          token: resetToken,
          password: newPassword,
          confirmPassword: newPassword
        })
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.token).toBeDefined();

      // Verify password was changed
      const updatedUser = await User.findById(user._id);
      const isNewPassword = await updatedUser.comparePassword(newPassword);
      expect(isNewPassword).toBe(true);

      // Verify reset token was cleared
      expect(updatedUser.passwordResetToken).toBeUndefined();
      expect(updatedUser.passwordResetExpires).toBeUndefined();
    });

    test('should not reset password with invalid token', async () => {
      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          token: 'invalid-token',
          password: 'newpassword123',
          confirmPassword: 'newpassword123'
        })
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('Invalid');
    });

    test('should not reset password with expired token', async () => {
      // Set token as expired
      user.passwordResetExpires = new Date(Date.now() - 60 * 60 * 1000); // 1 hour ago
      await user.save();

      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          token: resetToken,
          password: 'newpassword123',
          confirmPassword: 'newpassword123'
        })
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('expired');
    });

    test('should validate password confirmation', async () => {
      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          token: resetToken,
          password: 'newpassword123',
          confirmPassword: 'differentpassword'
        })
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('match');
    });
  });

  describe('GET /api/auth/me', () => {
    let user;
    let token;

    beforeEach(async () => {
      const userData = global.testUtils.createTestUser();
      user = new User(userData);
      await user.save();
      token = global.testUtils.generateTestToken(user._id);
    });

    test('should get current user with valid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.user.email).toBe(user.email);
      expect(response.body.data.user.firstName).toBe(user.firstName);
      expect(response.body.data.user.password).toBeUndefined(); // Should not include password
    });

    test('should not get user without token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .expect(401);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toContain('token');
    });

    test('should not get user with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.status).toBe('error');
    });
  });

  describe('POST /api/auth/logout', () => {
    let user;
    let token;

    beforeEach(async () => {
      const userData = global.testUtils.createTestUser();
      user = new User(userData);
      await user.save();
      token = global.testUtils.generateTestToken(user._id);
    });

    test('should logout successfully', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toContain('logout');
    });

    test('should handle logout without token', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .expect(401);

      expect(response.body.status).toBe('error');
    });
  });
});
