const logger = require('../utils/logger');

/**
 * Base Service Class
 * Provides common functionality for all service classes
 */
class BaseService {
  constructor(name) {
    this.serviceName = name;
    this.logger = logger;
  }

  /**
   * Log service operations
   * @param {string} level - Log level (info, error, warn, debug)
   * @param {string} message - Log message
   * @param {Object} meta - Additional metadata
   */
  log(level, message, meta = {}) {
    this.logger[level](`[${this.serviceName}] ${message}`, {
      service: this.serviceName,
      ...meta
    });
  }

  /**
   * Handle service errors consistently
   * @param {Error} error - The error object
   * @param {string} operation - The operation that failed
   * @param {Object} context - Additional context
   */
  handleError(error, operation, context = {}) {
    this.log('error', `${operation} failed: ${error.message}`, {
      error: error.stack,
      operation,
      ...context
    });
    throw error;
  }

  /**
   * Validate required parameters
   * @param {Object} params - Parameters to validate
   * @param {Array} required - Array of required parameter names
   */
  validateRequired(params, required) {
    const missing = required.filter(param => !params[param]);
    if (missing.length > 0) {
      throw new Error(`Missing required parameters: ${missing.join(', ')}`);
    }
  }

  /**
   * Format response data consistently
   * @param {*} data - The data to format
   * @param {string} message - Success message
   */
  formatResponse(data, message = 'Operation successful') {
    return {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Format error response consistently
   * @param {Error} error - The error object
   * @param {string} operation - The operation that failed
   */
  formatError(error, operation) {
    return {
      success: false,
      message: error.message,
      operation,
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = BaseService;
