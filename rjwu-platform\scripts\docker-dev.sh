#!/bin/bash

# RJWU Platform Docker Development Script
# This script provides convenient commands for Docker development workflow

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if docker-compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
}

# Function to build services
build_services() {
    print_status "Building Docker services..."
    docker-compose build --no-cache
    print_success "Services built successfully!"
}

# Function to start services
start_services() {
    print_status "Starting RJWU Platform services..."
    docker-compose up -d
    print_success "Services started successfully!"
    
    print_status "Waiting for services to be healthy..."
    sleep 10
    
    # Check service health
    check_service_health
}

# Function to stop services
stop_services() {
    print_status "Stopping RJWU Platform services..."
    docker-compose down
    print_success "Services stopped successfully!"
}

# Function to restart services
restart_services() {
    print_status "Restarting RJWU Platform services..."
    docker-compose restart
    print_success "Services restarted successfully!"
}

# Function to view logs
view_logs() {
    local service=$1
    if [ -z "$service" ]; then
        print_status "Showing logs for all services..."
        docker-compose logs -f
    else
        print_status "Showing logs for $service..."
        docker-compose logs -f "$service"
    fi
}

# Function to check service health
check_service_health() {
    print_status "Checking service health..."
    
    # Check MongoDB
    if docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
        print_success "MongoDB is healthy"
    else
        print_warning "MongoDB is not responding"
    fi
    
    # Check Redis
    if docker-compose exec redis redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is healthy"
    else
        print_warning "Redis is not responding"
    fi
    
    # Check Backend
    if curl -f http://localhost:5000/health > /dev/null 2>&1; then
        print_success "Backend is healthy"
    else
        print_warning "Backend is not responding"
    fi
    
    # Check Frontend
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        print_success "Frontend is healthy"
    else
        print_warning "Frontend is not responding"
    fi
}

# Function to clean up Docker resources
cleanup() {
    print_status "Cleaning up Docker resources..."
    
    # Stop and remove containers
    docker-compose down -v
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    # Remove unused networks
    docker network prune -f
    
    print_success "Cleanup completed!"
}

# Function to reset everything
reset() {
    print_warning "This will remove all containers, volumes, and rebuild everything."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup
        build_services
        start_services
        print_success "Reset completed!"
    else
        print_status "Reset cancelled."
    fi
}

# Function to run database migrations/seeding
setup_database() {
    print_status "Setting up database..."
    
    # Wait for MongoDB to be ready
    print_status "Waiting for MongoDB to be ready..."
    until docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; do
        sleep 2
    done
    
    # Run database setup if script exists
    if [ -f "./backend/scripts/setup-db.js" ]; then
        print_status "Running database setup script..."
        docker-compose exec backend node scripts/setup-db.js
        print_success "Database setup completed!"
    else
        print_warning "No database setup script found."
    fi
}

# Function to run tests
run_tests() {
    local service=$1
    if [ -z "$service" ]; then
        print_status "Running tests for all services..."
        docker-compose exec backend npm test
        docker-compose exec frontend npm test
    elif [ "$service" = "backend" ]; then
        print_status "Running backend tests..."
        docker-compose exec backend npm test
    elif [ "$service" = "frontend" ]; then
        print_status "Running frontend tests..."
        docker-compose exec frontend npm test
    else
        print_error "Unknown service: $service"
        exit 1
    fi
}

# Function to show help
show_help() {
    echo "RJWU Platform Docker Development Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build       Build all Docker services"
    echo "  start       Start all services"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  logs        View logs for all services"
    echo "  logs <svc>  View logs for specific service"
    echo "  health      Check health of all services"
    echo "  test        Run tests for all services"
    echo "  test <svc>  Run tests for specific service (backend/frontend)"
    echo "  setup-db    Setup database with initial data"
    echo "  cleanup     Clean up Docker resources"
    echo "  reset       Reset everything (rebuild and restart)"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs backend"
    echo "  $0 test frontend"
}

# Main script logic
main() {
    check_docker
    check_docker_compose
    
    case "${1:-help}" in
        build)
            build_services
            ;;
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            view_logs "$2"
            ;;
        health)
            check_service_health
            ;;
        test)
            run_tests "$2"
            ;;
        setup-db)
            setup_database
            ;;
        cleanup)
            cleanup
            ;;
        reset)
            reset
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
