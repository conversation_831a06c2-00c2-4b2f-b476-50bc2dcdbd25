const Course = require('../../src/models/Course');
const User = require('../../src/models/User');

describe('Course Model', () => {
  let instructor;

  beforeEach(async () => {
    // Create instructor user
    const instructorData = global.testUtils.createTestUser({
      role: 'instructor',
      email: '<EMAIL>'
    });
    instructor = new User(instructorData);
    await instructor.save();
  });

  describe('Course Creation', () => {
    test('should create a valid course', async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id
      });
      const course = new Course(courseData);
      const savedCourse = await course.save();

      expect(savedCourse._id).toBeDefined();
      expect(savedCourse.title).toBe(courseData.title);
      expect(savedCourse.description).toBe(courseData.description);
      expect(savedCourse.category).toBe(courseData.category);
      expect(savedCourse.level).toBe(courseData.level);
      expect(savedCourse.price).toBe(courseData.price);
      expect(savedCourse.instructor.toString()).toBe(instructor._id.toString());
    });

    test('should require required fields', async () => {
      const course = new Course({});
      
      await expect(course.save()).rejects.toThrow();
    });

    test('should validate price range', async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id,
        price: -100 // Invalid negative price
      });
      const course = new Course(courseData);

      await expect(course.save()).rejects.toThrow();
    });

    test('should validate level enum', async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id,
        level: 'invalid-level'
      });
      const course = new Course(courseData);

      await expect(course.save()).rejects.toThrow();
    });

    test('should set default values', async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id
      });
      delete courseData.enrollmentCount;
      delete courseData.rating;
      
      const course = new Course(courseData);
      await course.save();

      expect(course.enrollmentCount).toBe(0);
      expect(course.rating).toBe(0);
      expect(course.isActive).toBe(true);
      expect(course.status).toBe('draft');
    });
  });

  describe('Course Methods', () => {
    let course;

    beforeEach(async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id
      });
      course = new Course(courseData);
      await course.save();
    });

    test('should calculate average rating', async () => {
      course.reviews = [
        { rating: 5, comment: 'Great course!' },
        { rating: 4, comment: 'Good content' },
        { rating: 3, comment: 'Average' }
      ];

      course.calculateAverageRating();
      expect(course.rating).toBe(4); // (5+4+3)/3 = 4
    });

    test('should update enrollment count', async () => {
      expect(course.enrollmentCount).toBe(0);

      course.enrollmentCount += 1;
      await course.save();

      expect(course.enrollmentCount).toBe(1);
    });

    test('should check if course is published', () => {
      expect(course.isPublished).toBe(false);

      course.status = 'published';
      course.settings.isPublished = true;
      expect(course.isPublished).toBe(true);
    });

    test('should check if course is free', () => {
      expect(course.isFree).toBe(false);

      course.price = 0;
      expect(course.isFree).toBe(true);
    });
  });

  describe('Course Pricing', () => {
    test('should handle free courses', async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id,
        price: 0
      });
      const course = new Course(courseData);
      await course.save();

      expect(course.price).toBe(0);
      expect(course.isFree).toBe(true);
    });

    test('should handle paid courses', async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id,
        price: 1999
      });
      const course = new Course(courseData);
      await course.save();

      expect(course.price).toBe(1999);
      expect(course.isFree).toBe(false);
    });

    test('should handle discount pricing', async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id,
        pricing: {
          originalPrice: 2999,
          currentPrice: 1999,
          discountPercentage: 33,
          discountValidUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
        }
      });
      const course = new Course(courseData);
      await course.save();

      expect(course.pricing.originalPrice).toBe(2999);
      expect(course.pricing.currentPrice).toBe(1999);
      expect(course.pricing.discountPercentage).toBe(33);
    });
  });

  describe('Course Content', () => {
    test('should add chapters to course', async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id
      });
      const course = new Course(courseData);
      await course.save();

      course.chapters = [
        {
          title: 'Introduction',
          description: 'Course introduction',
          order: 1,
          duration: 30,
          isPublished: true
        },
        {
          title: 'Advanced Topics',
          description: 'Advanced course content',
          order: 2,
          duration: 60,
          isPublished: false
        }
      ];

      await course.save();

      expect(course.chapters).toHaveLength(2);
      expect(course.chapters[0].title).toBe('Introduction');
      expect(course.chapters[1].title).toBe('Advanced Topics');
    });

    test('should calculate total duration from chapters', async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id
      });
      const course = new Course(courseData);

      course.chapters = [
        { title: 'Chapter 1', duration: 30, order: 1 },
        { title: 'Chapter 2', duration: 45, order: 2 },
        { title: 'Chapter 3', duration: 25, order: 3 }
      ];

      await course.save();

      const totalDuration = course.chapters.reduce((sum, chapter) => sum + chapter.duration, 0);
      expect(totalDuration).toBe(100); // 30 + 45 + 25
    });
  });

  describe('Course Reviews', () => {
    let course;

    beforeEach(async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id
      });
      course = new Course(courseData);
      await course.save();
    });

    test('should add review to course', async () => {
      const studentData = global.testUtils.createTestUser({
        email: '<EMAIL>'
      });
      const student = new User(studentData);
      await student.save();

      course.reviews.push({
        user: student._id,
        rating: 5,
        comment: 'Excellent course!',
        createdAt: new Date()
      });

      await course.save();

      expect(course.reviews).toHaveLength(1);
      expect(course.reviews[0].rating).toBe(5);
      expect(course.reviews[0].comment).toBe('Excellent course!');
    });

    test('should calculate average rating from reviews', async () => {
      course.reviews = [
        { rating: 5, comment: 'Great!' },
        { rating: 4, comment: 'Good' },
        { rating: 5, comment: 'Excellent!' },
        { rating: 3, comment: 'Average' }
      ];

      course.calculateAverageRating();
      expect(course.rating).toBe(4.25); // (5+4+5+3)/4 = 4.25
    });
  });

  describe('Course Settings', () => {
    test('should handle course settings', async () => {
      const courseData = global.testUtils.createTestCourse({
        instructor: instructor._id,
        settings: {
          isPublished: true,
          allowComments: true,
          allowDownloads: false,
          language: 'english',
          difficulty: 'intermediate',
          prerequisites: ['Basic JavaScript'],
          learningOutcomes: ['Learn React', 'Build projects']
        }
      });
      const course = new Course(courseData);
      await course.save();

      expect(course.settings.isPublished).toBe(true);
      expect(course.settings.allowComments).toBe(true);
      expect(course.settings.allowDownloads).toBe(false);
      expect(course.settings.prerequisites).toContain('Basic JavaScript');
      expect(course.settings.learningOutcomes).toContain('Learn React');
    });
  });

  describe('Course Search', () => {
    beforeEach(async () => {
      // Create multiple courses for search testing
      const courses = [
        {
          title: 'JavaScript Fundamentals',
          description: 'Learn JavaScript basics',
          category: 'Programming',
          level: 'beginner',
          instructor: instructor._id
        },
        {
          title: 'React Advanced',
          description: 'Advanced React concepts',
          category: 'Programming',
          level: 'advanced',
          instructor: instructor._id
        },
        {
          title: 'Data Science Basics',
          description: 'Introduction to data science',
          category: 'Data Science',
          level: 'beginner',
          instructor: instructor._id
        }
      ];

      for (const courseData of courses) {
        const course = new Course(courseData);
        await course.save();
      }
    });

    test('should find courses by category', async () => {
      const programmingCourses = await Course.find({ category: 'Programming' });
      expect(programmingCourses).toHaveLength(2);
    });

    test('should find courses by level', async () => {
      const beginnerCourses = await Course.find({ level: 'beginner' });
      expect(beginnerCourses).toHaveLength(2);
    });

    test('should find courses by instructor', async () => {
      const instructorCourses = await Course.find({ instructor: instructor._id });
      expect(instructorCourses).toHaveLength(3);
    });
  });
});
