# App Configuration
NEXT_PUBLIC_APP_NAME=RJWU Platform
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NODE_ENV=development

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=rjwu-nextauth-secret-2024-dev
JWT_SECRET=rjwu-dev-jwt-secret-key-2024-change-in-production

# Database (Not used in frontend, but keeping for consistency)
DATABASE_URL=mongodb://localhost:27017/rjwu_dev

# Redis
REDIS_URL=redis://localhost:6379

# AWS Configuration (Development placeholders)
AWS_ACCESS_KEY_ID=dev_aws_access_key
AWS_SECRET_ACCESS_KEY=dev_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=rjwu-platform-dev-files

# Email Configuration (Development)
SMTP_HOST=smtp.ethereal.email
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=dev-password

# Socket.IO
NEXT_PUBLIC_SOCKET_URL=http://localhost:5000

# Analytics (Development - disabled)
NEXT_PUBLIC_GA_ID=G-DEVELOPMENT

# Feature Flags
NEXT_PUBLIC_ENABLE_PWA=true
NEXT_PUBLIC_ENABLE_OFFLINE=false
NEXT_PUBLIC_ENABLE_ANALYTICS=false

# Third-party Services (Development/Test keys)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_dev_stripe_key
STRIPE_SECRET_KEY=sk_test_dev_stripe_secret

NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_development_key

# Sentry Error Tracking (Development - disabled)
SENTRY_DSN=

# Development specific
NEXT_PUBLIC_DEBUG=true
NEXT_PUBLIC_LOG_LEVEL=debug
