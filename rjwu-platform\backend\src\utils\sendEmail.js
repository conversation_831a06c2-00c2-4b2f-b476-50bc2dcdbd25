const nodemailer = require('nodemailer');
const config = require('../config/config');
const logger = require('./logger');

// Create transporter
const createTransporter = () => {
  if (config.EMAIL_SERVICE === 'gmail') {
    return nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: config.EMAIL_FROM,
        pass: config.EMAIL_PASSWORD
      }
    });
  }

  // For other email services or SMTP
  return nodemailer.createTransporter({
    host: config.EMAIL_HOST,
    port: config.EMAIL_PORT,
    secure: config.EMAIL_PORT === 465, // true for 465, false for other ports
    auth: {
      user: config.EMAIL_FROM,
      pass: config.EMAIL_PASSWORD
    }
  });
};

const sendEmail = async (options) => {
  try {
    // For development, just log the email instead of sending
    if (config.EMAIL_SERVICE === 'console') {
      logger.info('Email would be sent:', {
        to: options.email,
        subject: options.subject,
        message: options.message
      });
      return { messageId: 'console-mode-' + Date.now() };
    }

    const transporter = createTransporter();

    const mailOptions = {
      from: `${config.EMAIL_FROM_NAME} <${config.EMAIL_FROM}>`,
      to: options.email,
      subject: options.subject,
      text: options.message,
      html: options.html || options.message.replace(/\n/g, '<br>')
    };

    const info = await transporter.sendMail(mailOptions);

    logger.info(`Email sent successfully to ${options.email}: ${info.messageId}`);
    return info;
  } catch (error) {
    logger.error('Email sending failed:', error);
    throw new Error('Email could not be sent');
  }
};

module.exports = sendEmail;
