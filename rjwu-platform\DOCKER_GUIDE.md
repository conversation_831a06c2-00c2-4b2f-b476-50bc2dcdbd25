# Docker Guide for RJWU Platform

## Overview

This guide provides comprehensive information about using Docker with the RJWU EduTech Platform. The platform uses Docker for consistent development environments, easy deployment, and scalable production infrastructure.

## Architecture

The Docker setup consists of the following services:

### Development Environment
- **Backend**: Node.js/Express API server
- **Frontend**: Next.js application with hot reload
- **MongoDB**: Database for application data
- **Redis**: Cache and session storage

### Production Environment
- **Backend**: Optimized Node.js production build
- **Frontend**: Static Next.js build with standalone server
- **MongoDB**: Production database with authentication
- **Redis**: Production cache with password protection
- **Nginx**: Reverse proxy with SSL termination and load balancing

## Docker Files

### Backend Dockerfile
- **Multi-stage build** for development and production
- **Security**: Non-root user, minimal attack surface
- **Optimization**: Layer caching, dependency optimization
- **Health checks**: Built-in health monitoring

### Frontend Dockerfile
- **Next.js optimized** build process
- **Static asset optimization** for production
- **Standalone deployment** for minimal container size
- **Security**: Non-root user, secure defaults

## Quick Start

### Prerequisites
- Docker Desktop installed and running
- Docker Compose v2.0 or higher
- Git for cloning the repository

### Development Setup

1. **Clone and navigate to the project**:
   ```bash
   git clone <repository-url>
   cd rjwu-platform
   ```

2. **Start development environment**:
   ```bash
   # Using the convenience script (Windows)
   scripts\docker-dev.bat start
   
   # Or using docker-compose directly
   docker-compose up -d
   ```

3. **Check service health**:
   ```bash
   scripts\docker-dev.bat health
   ```

4. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000
   - API Documentation: http://localhost:5000/api-docs
   - MongoDB: localhost:27017
   - Redis: localhost:6379

## Development Workflow

### Using Convenience Scripts

**Windows (PowerShell/CMD)**:
```cmd
# Start all services
scripts\docker-dev.bat start

# View logs
scripts\docker-dev.bat logs
scripts\docker-dev.bat logs backend

# Run tests
scripts\docker-dev.bat test
scripts\docker-dev.bat test frontend

# Stop services
scripts\docker-dev.bat stop

# Clean up and reset
scripts\docker-dev.bat reset
```

**Linux/macOS (Bash)**:
```bash
# Make script executable
chmod +x scripts/docker-dev.sh

# Start all services
./scripts/docker-dev.sh start

# View logs
./scripts/docker-dev.sh logs
./scripts/docker-dev.sh logs backend

# Run tests
./scripts/docker-dev.sh test
./scripts/docker-dev.sh test frontend

# Stop services
./scripts/docker-dev.sh stop

# Clean up and reset
./scripts/docker-dev.sh reset
```

### Manual Docker Commands

```bash
# Build services
docker-compose build

# Start services in background
docker-compose up -d

# View logs
docker-compose logs -f
docker-compose logs -f backend

# Execute commands in containers
docker-compose exec backend npm test
docker-compose exec frontend npm run build

# Stop services
docker-compose down

# Remove volumes (careful - this deletes data)
docker-compose down -v
```

## Production Deployment

### Environment Variables

Create a `.env.prod` file with production values:

```env
# Database
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=secure_password
MONGO_DATABASE=rjwu_prod
MONGODB_URI=************************************************************************
REDIS_PASSWORD=redis_secure_password
REDIS_URL=redis://:redis_secure_password@redis:6379

# Application
JWT_SECRET=production_jwt_secret_very_long_and_secure
SESSION_SECRET=production_session_secret_very_long_and_secure
FRONTEND_URL=https://yourdomain.com
NEXT_PUBLIC_API_URL=https://yourdomain.com/api

# Email (Production SMTP)
EMAIL_HOST=smtp.youremailprovider.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=email_password

# File Storage (Production Cloudinary)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# Payment Gateways (Production keys)
RAZORPAY_KEY_ID=rzp_live_your_key
RAZORPAY_KEY_SECRET=your_live_secret
```

### Production Deployment

```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Start production services
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d

# Check health
curl https://yourdomain.com/health
```

## Service Configuration

### MongoDB
- **Development**: No authentication, data persisted in volume
- **Production**: Authentication enabled, secure configuration
- **Backup**: Regular backups recommended for production

### Redis
- **Development**: No password, used for caching and sessions
- **Production**: Password protected, persistent storage

### Backend
- **Development**: Hot reload, debug logging, development dependencies
- **Production**: Optimized build, production logging, security hardened

### Frontend
- **Development**: Next.js dev server with hot reload
- **Production**: Static build with standalone server

## Health Monitoring

### Health Check Endpoints

**Backend**:
- `GET /health` - Comprehensive health check
- `GET /ready` - Readiness probe
- `GET /live` - Liveness probe

**Frontend**:
- `GET /api/health` - Frontend and backend connectivity check

### Docker Health Checks

All services include Docker health checks:
- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Retries**: 3
- **Start Period**: 40 seconds

### Monitoring Commands

```bash
# Check container health
docker-compose ps

# View health check logs
docker inspect <container_name> | grep -A 10 Health

# Monitor resource usage
docker stats
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**:
   ```bash
   # Check what's using the port
   netstat -ano | findstr :3000
   
   # Kill the process or change ports in docker-compose.yml
   ```

2. **Permission Issues**:
   ```bash
   # Reset file permissions (Linux/macOS)
   sudo chown -R $USER:$USER .
   
   # On Windows, run Docker Desktop as administrator
   ```

3. **Database Connection Issues**:
   ```bash
   # Check MongoDB logs
   docker-compose logs mongodb
   
   # Verify connection string in environment variables
   ```

4. **Build Failures**:
   ```bash
   # Clean build cache
   docker system prune -a
   
   # Rebuild without cache
   docker-compose build --no-cache
   ```

### Debugging

```bash
# Access container shell
docker-compose exec backend sh
docker-compose exec frontend sh

# View container logs
docker-compose logs -f --tail=100 backend

# Check container resource usage
docker stats rjwu-backend rjwu-frontend

# Inspect container configuration
docker inspect rjwu-backend
```

## Performance Optimization

### Development
- Use volume mounts for hot reload
- Optimize .dockerignore files
- Use multi-stage builds for faster rebuilds

### Production
- Use production-optimized images
- Enable gzip compression in Nginx
- Configure proper caching headers
- Use CDN for static assets

## Security Considerations

### Development
- Use development secrets (already configured)
- Limit exposed ports
- Regular dependency updates

### Production
- Use strong, unique secrets
- Enable SSL/TLS with proper certificates
- Configure firewall rules
- Regular security updates
- Monitor for vulnerabilities

## Backup and Recovery

### Database Backup
```bash
# Create MongoDB backup
docker-compose exec mongodb mongodump --out /backup

# Copy backup from container
docker cp rjwu-mongodb:/backup ./mongodb-backup
```

### Volume Backup
```bash
# Backup all volumes
docker run --rm -v rjwu-platform_mongodb_data:/data -v $(pwd):/backup alpine tar czf /backup/mongodb-backup.tar.gz /data
```

## Scaling

### Horizontal Scaling
```bash
# Scale backend service
docker-compose up -d --scale backend=3

# Use load balancer (Nginx) for distribution
```

### Resource Limits
Configure in docker-compose.prod.yml:
```yaml
deploy:
  resources:
    limits:
      memory: 512M
      cpus: '0.5'
    reservations:
      memory: 256M
      cpus: '0.25'
```

## CI/CD Integration

### GitHub Actions Example
```yaml
- name: Build and test
  run: |
    docker-compose build
    docker-compose run backend npm test
    docker-compose run frontend npm test

- name: Deploy to production
  run: |
    docker-compose -f docker-compose.prod.yml up -d
```

## Support

For Docker-related issues:
1. Check this documentation
2. Review container logs
3. Verify environment configuration
4. Check Docker Desktop status
5. Consult Docker documentation
