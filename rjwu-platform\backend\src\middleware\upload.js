const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { AppError } = require('./errorHandler');
const config = require('../config/config');
const logger = require('../utils/logger');

/**
 * Upload Middleware
 * Handles file uploads with validation and storage configuration
 */

// Ensure upload directories exist
const ensureUploadDirs = () => {
  const dirs = [
    'uploads',
    'uploads/avatars',
    'uploads/course-thumbnails',
    'uploads/documents',
    'uploads/videos',
    'uploads/temp'
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      logger.info(`Created upload directory: ${dir}`);
    }
  });
};

// Initialize upload directories
ensureUploadDirs();

// File type configurations
const fileTypes = {
  avatar: {
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    maxSize: 5 * 1024 * 1024, // 5MB
    destination: 'uploads/avatars'
  },
  course_thumbnail: {
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    maxSize: 10 * 1024 * 1024, // 10MB
    destination: 'uploads/course-thumbnails'
  },
  document: {
    allowedTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ],
    maxSize: 50 * 1024 * 1024, // 50MB
    destination: 'uploads/documents'
  },
  video: {
    allowedTypes: ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'],
    maxSize: 500 * 1024 * 1024, // 500MB
    destination: 'uploads/videos'
  }
};

/**
 * Generate unique filename
 * @param {Object} file - Multer file object
 * @param {String} prefix - Filename prefix
 * @returns {String} Unique filename
 */
const generateFilename = (file, prefix = '') => {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = path.extname(file.originalname);
  return `${prefix}${timestamp}-${randomString}${extension}`;
};

/**
 * Storage configuration for different file types
 */
const createStorage = (uploadType) => {
  return multer.diskStorage({
    destination: (req, file, cb) => {
      const config = fileTypes[uploadType] || fileTypes.document;
      cb(null, config.destination);
    },
    filename: (req, file, cb) => {
      const prefix = uploadType === 'avatar' ? 'avatar-' : 
                    uploadType === 'course_thumbnail' ? 'thumb-' :
                    uploadType === 'video' ? 'video-' : 'doc-';
      const filename = generateFilename(file, prefix);
      cb(null, filename);
    }
  });
};

/**
 * File filter function
 * @param {String} uploadType - Type of upload
 * @returns {Function} Multer file filter function
 */
const createFileFilter = (uploadType) => {
  return (req, file, cb) => {
    const config = fileTypes[uploadType] || fileTypes.document;
    
    if (config.allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new AppError(
        `Invalid file type. Allowed types: ${config.allowedTypes.join(', ')}`,
        400
      ), false);
    }
  };
};

/**
 * Create multer upload middleware
 * @param {String} uploadType - Type of upload
 * @param {String} fieldName - Form field name
 * @param {Boolean} multiple - Allow multiple files
 * @returns {Function} Multer middleware
 */
const createUploadMiddleware = (uploadType, fieldName = 'file', multiple = false) => {
  const config = fileTypes[uploadType] || fileTypes.document;
  
  const upload = multer({
    storage: createStorage(uploadType),
    fileFilter: createFileFilter(uploadType),
    limits: {
      fileSize: config.maxSize,
      files: multiple ? 10 : 1
    }
  });

  return multiple ? upload.array(fieldName, 10) : upload.single(fieldName);
};

/**
 * Avatar upload middleware
 */
const uploadAvatar = createUploadMiddleware('avatar', 'avatar');

/**
 * Course thumbnail upload middleware
 */
const uploadCourseThumbnail = createUploadMiddleware('course_thumbnail', 'thumbnail');

/**
 * Document upload middleware
 */
const uploadDocument = createUploadMiddleware('document', 'document');

/**
 * Multiple documents upload middleware
 */
const uploadDocuments = createUploadMiddleware('document', 'documents', true);

/**
 * Video upload middleware
 */
const uploadVideo = createUploadMiddleware('video', 'video');

/**
 * Multiple videos upload middleware
 */
const uploadVideos = createUploadMiddleware('video', 'videos', true);

/**
 * Generic file upload middleware
 */
const uploadFile = multer({
  storage: multer.diskStorage({
    destination: 'uploads/temp',
    filename: (req, file, cb) => {
      const filename = generateFilename(file, 'temp-');
      cb(null, filename);
    }
  }),
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB
  }
}).single('file');

/**
 * File validation middleware
 * Validates uploaded files after multer processing
 */
const validateUploadedFile = (req, res, next) => {
  try {
    if (!req.file && !req.files) {
      return next(new AppError('No file uploaded', 400));
    }

    const files = req.files || [req.file];
    
    for (const file of files) {
      // Check if file was actually uploaded
      if (!fs.existsSync(file.path)) {
        return next(new AppError('File upload failed', 500));
      }

      // Add file metadata
      file.uploadedAt = new Date();
      file.uploadedBy = req.user ? req.user.id : null;
      
      // Log successful upload
      logger.info(`File uploaded successfully: ${file.filename}`, {
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype,
        uploadedBy: file.uploadedBy
      });
    }

    next();
  } catch (error) {
    logger.error('File validation error:', error);
    next(new AppError('File validation failed', 500));
  }
};

/**
 * Clean up temporary files
 * @param {String} filePath - Path to file to delete
 */
const cleanupFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logger.info(`Cleaned up file: ${filePath}`);
    }
  } catch (error) {
    logger.error(`Failed to cleanup file ${filePath}:`, error);
  }
};

/**
 * Error handling middleware for multer
 */
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    let message = 'File upload error';
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        message = 'File size too large';
        break;
      case 'LIMIT_FILE_COUNT':
        message = 'Too many files uploaded';
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        message = 'Unexpected file field';
        break;
      case 'LIMIT_PART_COUNT':
        message = 'Too many parts in multipart form';
        break;
      default:
        message = error.message;
    }
    
    logger.warn('Multer upload error:', { code: error.code, message });
    return next(new AppError(message, 400));
  }
  
  next(error);
};

/**
 * Get file URL helper
 * @param {String} filename - Filename
 * @param {String} type - File type
 * @returns {String} File URL
 */
const getFileUrl = (filename, type = 'document') => {
  const baseUrl = config.FRONTEND_URL || 'http://localhost:3000';
  const typeMap = {
    avatar: 'avatars',
    course_thumbnail: 'course-thumbnails',
    document: 'documents',
    video: 'videos'
  };
  
  const folder = typeMap[type] || 'documents';
  return `${baseUrl}/uploads/${folder}/${filename}`;
};

module.exports = {
  // Upload middleware
  uploadAvatar,
  uploadCourseThumbnail,
  uploadDocument,
  uploadDocuments,
  uploadVideo,
  uploadVideos,
  uploadFile,
  
  // Validation and error handling
  validateUploadedFile,
  handleUploadError,
  
  // Utilities
  cleanupFile,
  getFileUrl,
  ensureUploadDirs,
  
  // File type configurations
  fileTypes
};
