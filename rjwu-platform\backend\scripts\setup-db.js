const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const config = require('../src/config/config');
const logger = require('../src/utils/logger');

// Import models
const User = require('../src/models/User');
const Course = require('../src/models/Course');
const Batch = require('../src/models/Batch');
const Payment = require('../src/models/Payment');

/**
 * Database Setup Script
 * Creates sample data for development and testing
 */

async function connectDatabase() {
  try {
    await mongoose.connect(config.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    logger.info('Connected to MongoDB for database setup');
  } catch (error) {
    logger.error('Database connection failed:', error);
    process.exit(1);
  }
}

async function clearDatabase() {
  try {
    logger.info('Clearing existing data...');
    
    // Clear all collections
    await User.deleteMany({});
    await Course.deleteMany({});
    await Batch.deleteMany({});
    await Payment.deleteMany({});
    
    logger.info('Database cleared successfully');
  } catch (error) {
    logger.error('Error clearing database:', error);
    throw error;
  }
}

async function createUsers() {
  try {
    logger.info('Creating sample users...');
    
    const users = [
      // Admin Users
      {
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        password: await bcrypt.hash('admin123', 12),
        role: 'admin',
        isEmailVerified: true,
        isPhoneVerified: true,
        isActive: true,
        address: {
          street: 'RJWU Campus',
          city: 'Jaipur',
          state: 'Rajasthan',
          pincode: '302017',
          country: 'India'
        }
      },
      
      // Instructor Users
      {
        firstName: 'Dr. Sarah',
        lastName: 'Wilson',
        email: '<EMAIL>',
        phone: '+91 9876543211',
        password: await bcrypt.hash('instructor123', 12),
        role: 'instructor',
        isEmailVerified: true,
        isPhoneVerified: true,
        isActive: true,
        instructorProfile: {
          bio: 'Senior Software Engineer with 8+ years of experience in React development. Former Facebook engineer.',
          experience: 8,
          specializations: ['React.js', 'JavaScript', 'Frontend Development', 'Web Development'],
          rating: 4.8,
          totalStudents: 1250,
          totalCourses: 5
        },
        address: {
          street: '123 Tech Street',
          city: 'Jaipur',
          state: 'Rajasthan',
          pincode: '302001',
          country: 'India'
        }
      },
      {
        firstName: 'Prof. Michael',
        lastName: 'Chen',
        email: '<EMAIL>',
        phone: '+91 9876543212',
        password: await bcrypt.hash('instructor123', 12),
        role: 'instructor',
        isEmailVerified: true,
        isPhoneVerified: true,
        isActive: true,
        instructorProfile: {
          bio: 'Full-stack developer and educator with expertise in Node.js and backend technologies.',
          experience: 10,
          specializations: ['Node.js', 'Express.js', 'MongoDB', 'Backend Development'],
          rating: 4.9,
          totalStudents: 890,
          totalCourses: 3
        },
        address: {
          street: '456 Code Avenue',
          city: 'Jaipur',
          state: 'Rajasthan',
          pincode: '302002',
          country: 'India'
        }
      },
      {
        firstName: 'Dr. Emily',
        lastName: 'Rodriguez',
        email: '<EMAIL>',
        phone: '+91 9876543213',
        password: await bcrypt.hash('instructor123', 12),
        role: 'instructor',
        isEmailVerified: true,
        isPhoneVerified: true,
        isActive: true,
        instructorProfile: {
          bio: 'Data scientist and machine learning expert with PhD in Computer Science.',
          experience: 12,
          specializations: ['Python', 'Data Science', 'Machine Learning', 'AI'],
          rating: 4.7,
          totalStudents: 1680,
          totalCourses: 7
        },
        address: {
          street: '789 Data Lane',
          city: 'Jaipur',
          state: 'Rajasthan',
          pincode: '302003',
          country: 'India'
        }
      },
      
      // Student Users
      {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+91 9876543214',
        password: await bcrypt.hash('student123', 12),
        role: 'student',
        isEmailVerified: true,
        isPhoneVerified: true,
        isActive: true,
        education: {
          highestQualification: 'Bachelor\'s Degree',
          fieldOfStudy: 'Computer Science',
          institution: 'University of Rajasthan',
          graduationYear: 2020
        },
        address: {
          street: '321 Student Street',
          city: 'Jaipur',
          state: 'Rajasthan',
          pincode: '302004',
          country: 'India'
        }
      },
      {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+91 9876543215',
        password: await bcrypt.hash('student123', 12),
        role: 'student',
        isEmailVerified: true,
        isPhoneVerified: true,
        isActive: true,
        education: {
          highestQualification: 'Master\'s Degree',
          fieldOfStudy: 'Information Technology',
          institution: 'Rajasthan Technical University',
          graduationYear: 2022
        },
        address: {
          street: '654 Learning Road',
          city: 'Jaipur',
          state: 'Rajasthan',
          pincode: '302005',
          country: 'India'
        }
      },
      {
        firstName: 'Raj',
        lastName: 'Patel',
        email: '<EMAIL>',
        phone: '+91 9876543216',
        password: await bcrypt.hash('student123', 12),
        role: 'student',
        isEmailVerified: true,
        isPhoneVerified: true,
        isActive: true,
        education: {
          highestQualification: 'Diploma',
          fieldOfStudy: 'Web Development',
          institution: 'Rajasthan Polytechnic',
          graduationYear: 2021
        },
        address: {
          street: '987 Knowledge Park',
          city: 'Jaipur',
          state: 'Rajasthan',
          pincode: '302006',
          country: 'India'
        }
      }
    ];
    
    const createdUsers = await User.insertMany(users);
    logger.info(`Created ${createdUsers.length} users`);
    
    return createdUsers;
  } catch (error) {
    logger.error('Error creating users:', error);
    throw error;
  }
}

async function createCourses(users) {
  try {
    logger.info('Creating sample courses...');
    
    // Find instructors
    const instructors = users.filter(user => user.role === 'instructor');
    
    const courses = [
      {
        title: 'Complete React.js Development Course',
        description: 'Master React.js from basics to advanced concepts including hooks, context, and modern patterns.',
        longDescription: `This comprehensive React.js course is designed to take you from a complete beginner to an advanced React developer. 
        You'll learn all the fundamental concepts of React including components, props, state, and event handling, 
        then progress to advanced topics like hooks, context API, performance optimization, and testing.`,
        instructor: instructors[0]._id, // Dr. Sarah Wilson
        category: 'Web Development',
        level: 'intermediate',
        price: 2999,
        originalPrice: 4999,
        duration: 40,
        language: 'english',
        thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800',
        status: 'published',
        isActive: true,
        settings: {
          isPublished: true,
          allowComments: true,
          allowDownloads: true,
          prerequisites: ['Basic HTML, CSS, and JavaScript', 'Familiarity with ES6+ features'],
          learningOutcomes: [
            'Build modern React applications from scratch',
            'Master React Hooks and functional components',
            'Understand state management with Context API',
            'Implement routing with React Router',
            'Write unit and integration tests',
            'Deploy React applications to production'
          ]
        },
        chapters: [
          {
            title: 'Getting Started with React',
            description: 'Introduction to React and setting up development environment',
            order: 1,
            duration: 120,
            isPublished: true,
            sections: [
              { title: 'Introduction to React', duration: 15, order: 1, isPublished: true },
              { title: 'Setting up Development Environment', duration: 20, order: 2, isPublished: true },
              { title: 'Your First React Component', duration: 18, order: 3, isPublished: true },
              { title: 'JSX Fundamentals', duration: 22, order: 4, isPublished: true }
            ]
          },
          {
            title: 'Components and Props',
            description: 'Understanding React components and data flow',
            order: 2,
            duration: 180,
            isPublished: true,
            sections: [
              { title: 'Functional vs Class Components', duration: 25, order: 1, isPublished: true },
              { title: 'Props and Component Communication', duration: 30, order: 2, isPublished: true },
              { title: 'Component Composition', duration: 20, order: 3, isPublished: true }
            ]
          }
        ],
        enrollmentCount: 1250,
        rating: 4.7,
        reviews: [
          {
            user: null, // Will be set after students are created
            rating: 5,
            comment: 'Excellent course! Very well structured and easy to follow.',
            createdAt: new Date('2024-01-05')
          }
        ]
      },
      {
        title: 'Node.js Backend Development Masterclass',
        description: 'Build scalable backend applications with Node.js, Express, and MongoDB.',
        longDescription: `Learn to build robust, scalable backend applications using Node.js and Express.js. 
        This course covers everything from basic server setup to advanced topics like authentication, 
        database integration, API design, and deployment.`,
        instructor: instructors[1]._id, // Prof. Michael Chen
        category: 'Backend Development',
        level: 'advanced',
        price: 3499,
        originalPrice: 5999,
        duration: 35,
        language: 'english',
        thumbnail: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=800',
        status: 'published',
        isActive: true,
        settings: {
          isPublished: true,
          allowComments: true,
          allowDownloads: true,
          prerequisites: ['JavaScript fundamentals', 'Basic understanding of web development'],
          learningOutcomes: [
            'Build RESTful APIs with Express.js',
            'Implement authentication and authorization',
            'Work with MongoDB and Mongoose',
            'Handle file uploads and processing',
            'Deploy Node.js applications',
            'Implement real-time features with Socket.io'
          ]
        },
        chapters: [
          {
            title: 'Node.js Fundamentals',
            description: 'Understanding Node.js runtime and core modules',
            order: 1,
            duration: 150,
            isPublished: true,
            sections: [
              { title: 'Introduction to Node.js', duration: 20, order: 1, isPublished: true },
              { title: 'Node.js Runtime and Event Loop', duration: 25, order: 2, isPublished: true },
              { title: 'Working with Modules', duration: 30, order: 3, isPublished: true }
            ]
          }
        ],
        enrollmentCount: 890,
        rating: 4.8,
        reviews: []
      },
      {
        title: 'Python for Data Science and Machine Learning',
        description: 'Complete guide to data science using Python, pandas, numpy, and scikit-learn.',
        longDescription: `Comprehensive course covering Python programming for data science and machine learning. 
        Learn data manipulation, visualization, statistical analysis, and machine learning algorithms.`,
        instructor: instructors[2]._id, // Dr. Emily Rodriguez
        category: 'Data Science',
        level: 'intermediate',
        price: 3999,
        originalPrice: 6999,
        duration: 45,
        language: 'english',
        thumbnail: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800',
        status: 'published',
        isActive: true,
        settings: {
          isPublished: true,
          allowComments: true,
          allowDownloads: true,
          prerequisites: ['Basic programming knowledge', 'High school mathematics'],
          learningOutcomes: [
            'Master Python for data analysis',
            'Work with pandas and numpy',
            'Create data visualizations',
            'Implement machine learning algorithms',
            'Build predictive models',
            'Deploy ML models to production'
          ]
        },
        chapters: [
          {
            title: 'Python Fundamentals for Data Science',
            description: 'Python basics and data science libraries',
            order: 1,
            duration: 200,
            isPublished: true,
            sections: [
              { title: 'Python Basics Review', duration: 30, order: 1, isPublished: true },
              { title: 'NumPy for Numerical Computing', duration: 45, order: 2, isPublished: true },
              { title: 'Pandas for Data Manipulation', duration: 60, order: 3, isPublished: true }
            ]
          }
        ],
        enrollmentCount: 1680,
        rating: 4.5,
        reviews: []
      }
    ];
    
    const createdCourses = await Course.insertMany(courses);
    logger.info(`Created ${createdCourses.length} courses`);
    
    return createdCourses;
  } catch (error) {
    logger.error('Error creating courses:', error);
    throw error;
  }
}

async function enrollStudentsInCourses(users, courses) {
  try {
    logger.info('Enrolling students in courses...');
    
    const students = users.filter(user => user.role === 'student');
    
    // Enroll John Doe in React and Node.js courses
    const johnDoe = students.find(s => s.email === '<EMAIL>');
    if (johnDoe) {
      johnDoe.enrolledCourses = [
        {
          course: courses[0]._id, // React course
          enrollmentDate: new Date('2023-12-01'),
          progress: 75,
          status: 'active'
        },
        {
          course: courses[1]._id, // Node.js course
          enrollmentDate: new Date('2023-12-15'),
          progress: 45,
          status: 'active'
        }
      ];
      await johnDoe.save();
    }
    
    // Enroll Jane Smith in Python course
    const janeSmith = students.find(s => s.email === '<EMAIL>');
    if (janeSmith) {
      janeSmith.enrolledCourses = [
        {
          course: courses[2]._id, // Python course
          enrollmentDate: new Date('2024-01-01'),
          progress: 20,
          status: 'active'
        }
      ];
      await janeSmith.save();
    }
    
    // Enroll Raj Patel in React course
    const rajPatel = students.find(s => s.email === '<EMAIL>');
    if (rajPatel) {
      rajPatel.enrolledCourses = [
        {
          course: courses[0]._id, // React course
          enrollmentDate: new Date('2024-01-10'),
          progress: 10,
          status: 'active'
        }
      ];
      await rajPatel.save();
    }
    
    logger.info('Students enrolled in courses successfully');
  } catch (error) {
    logger.error('Error enrolling students:', error);
    throw error;
  }
}

async function createBatches(users, courses) {
  try {
    logger.info('Creating sample batches...');
    
    const instructors = users.filter(user => user.role === 'instructor');
    
    const batches = [
      {
        name: 'React Development Batch - January 2024',
        description: 'Intensive React.js development batch with live sessions and projects',
        course: courses[0]._id, // React course
        instructor: instructors[0]._id, // Dr. Sarah Wilson
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-03-15'),
        maxStudents: 50,
        currentStudents: 25,
        schedule: {
          days: ['monday', 'wednesday', 'friday'],
          startTime: '10:00',
          endTime: '11:30',
          timezone: 'Asia/Kolkata'
        },
        status: 'active',
        isActive: true,
        students: [], // Will be populated separately
        sessions: [
          {
            title: 'Introduction to React',
            description: 'Getting started with React development',
            scheduledDate: new Date('2024-01-15T10:00:00Z'),
            duration: 90,
            type: 'live',
            status: 'completed'
          },
          {
            title: 'Components and JSX',
            description: 'Understanding React components and JSX syntax',
            scheduledDate: new Date('2024-01-17T10:00:00Z'),
            duration: 90,
            type: 'live',
            status: 'completed'
          }
        ]
      },
      {
        name: 'Backend Development with Node.js - February 2024',
        description: 'Advanced backend development using Node.js and Express',
        course: courses[1]._id, // Node.js course
        instructor: instructors[1]._id, // Prof. Michael Chen
        startDate: new Date('2024-02-01'),
        endDate: new Date('2024-04-01'),
        maxStudents: 30,
        currentStudents: 18,
        schedule: {
          days: ['tuesday', 'thursday'],
          startTime: '14:00',
          endTime: '16:00',
          timezone: 'Asia/Kolkata'
        },
        status: 'active',
        isActive: true,
        students: [],
        sessions: []
      }
    ];
    
    const createdBatches = await Batch.insertMany(batches);
    logger.info(`Created ${createdBatches.length} batches`);
    
    return createdBatches;
  } catch (error) {
    logger.error('Error creating batches:', error);
    throw error;
  }
}

async function createSamplePayments(users, courses) {
  try {
    logger.info('Creating sample payments...');
    
    const students = users.filter(user => user.role === 'student');
    
    const payments = [
      {
        user: students[0]._id, // John Doe
        course: courses[0]._id, // React course
        amount: 2999,
        currency: 'INR',
        gateway: 'razorpay',
        gatewayOrderId: 'order_test_001',
        gatewayPaymentId: 'pay_test_001',
        status: 'completed',
        verifiedAt: new Date('2023-12-01'),
        metadata: {
          receipt: 'course_react_user_john_001',
          gatewayResponse: { id: 'order_test_001' }
        }
      },
      {
        user: students[1]._id, // Jane Smith
        course: courses[2]._id, // Python course
        amount: 3999,
        currency: 'INR',
        gateway: 'razorpay',
        gatewayOrderId: 'order_test_002',
        gatewayPaymentId: 'pay_test_002',
        status: 'completed',
        verifiedAt: new Date('2024-01-01'),
        metadata: {
          receipt: 'course_python_user_jane_002',
          gatewayResponse: { id: 'order_test_002' }
        }
      }
    ];
    
    const createdPayments = await Payment.insertMany(payments);
    logger.info(`Created ${createdPayments.length} payments`);
    
    return createdPayments;
  } catch (error) {
    logger.error('Error creating payments:', error);
    throw error;
  }
}

async function setupDatabase() {
  try {
    logger.info('Starting database setup...');
    
    await connectDatabase();
    await clearDatabase();
    
    const users = await createUsers();
    const courses = await createCourses(users);
    await enrollStudentsInCourses(users, courses);
    const batches = await createBatches(users, courses);
    const payments = await createSamplePayments(users, courses);
    
    logger.info('Database setup completed successfully!');
    logger.info('Sample data created:');
    logger.info(`- Users: ${users.length}`);
    logger.info(`- Courses: ${courses.length}`);
    logger.info(`- Batches: ${batches.length}`);
    logger.info(`- Payments: ${payments.length}`);
    
    logger.info('\nSample login credentials:');
    logger.info('Admin: <EMAIL> / admin123');
    logger.info('Instructor: <EMAIL> / instructor123');
    logger.info('Student: <EMAIL> / student123');
    
  } catch (error) {
    logger.error('Database setup failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    logger.info('Database connection closed');
  }
}

// Run setup if called directly
if (require.main === module) {
  setupDatabase();
}

module.exports = {
  setupDatabase,
  clearDatabase,
  createUsers,
  createCourses,
  createBatches,
  createSamplePayments
};
