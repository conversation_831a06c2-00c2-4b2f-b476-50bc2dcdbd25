@echo off
REM RJWU Platform Docker Development Script for Windows
REM This script provides convenient commands for Docker development workflow

setlocal enabledelayedexpansion

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker is not running. Please start Docker and try again.
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] docker-compose is not installed. Please install docker-compose and try again.
    exit /b 1
)

REM Get the command
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=help

REM Execute based on command
if "%COMMAND%"=="build" goto build
if "%COMMAND%"=="start" goto start
if "%COMMAND%"=="stop" goto stop
if "%COMMAND%"=="restart" goto restart
if "%COMMAND%"=="logs" goto logs
if "%COMMAND%"=="health" goto health
if "%COMMAND%"=="test" goto test
if "%COMMAND%"=="setup-db" goto setup-db
if "%COMMAND%"=="cleanup" goto cleanup
if "%COMMAND%"=="reset" goto reset
if "%COMMAND%"=="help" goto help
goto unknown

:build
echo [INFO] Building Docker services...
docker-compose build --no-cache
if errorlevel 1 (
    echo [ERROR] Failed to build services
    exit /b 1
)
echo [SUCCESS] Services built successfully!
goto end

:start
echo [INFO] Starting RJWU Platform services...
docker-compose up -d
if errorlevel 1 (
    echo [ERROR] Failed to start services
    exit /b 1
)
echo [SUCCESS] Services started successfully!
echo [INFO] Waiting for services to be healthy...
timeout /t 10 /nobreak >nul
goto health

:stop
echo [INFO] Stopping RJWU Platform services...
docker-compose down
echo [SUCCESS] Services stopped successfully!
goto end

:restart
echo [INFO] Restarting RJWU Platform services...
docker-compose restart
echo [SUCCESS] Services restarted successfully!
goto end

:logs
if "%2"=="" (
    echo [INFO] Showing logs for all services...
    docker-compose logs -f
) else (
    echo [INFO] Showing logs for %2...
    docker-compose logs -f %2
)
goto end

:health
echo [INFO] Checking service health...

REM Check MongoDB
docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] MongoDB is not responding
) else (
    echo [SUCCESS] MongoDB is healthy
)

REM Check Redis
docker-compose exec redis redis-cli ping >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Redis is not responding
) else (
    echo [SUCCESS] Redis is healthy
)

REM Check Backend (using curl if available, otherwise skip)
curl -f http://localhost:5000/health >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Backend is not responding
) else (
    echo [SUCCESS] Backend is healthy
)

REM Check Frontend (using curl if available, otherwise skip)
curl -f http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Frontend is not responding
) else (
    echo [SUCCESS] Frontend is healthy
)
goto end

:test
if "%2"=="" (
    echo [INFO] Running tests for all services...
    docker-compose exec backend npm test
    docker-compose exec frontend npm test
) else if "%2"=="backend" (
    echo [INFO] Running backend tests...
    docker-compose exec backend npm test
) else if "%2"=="frontend" (
    echo [INFO] Running frontend tests...
    docker-compose exec frontend npm test
) else (
    echo [ERROR] Unknown service: %2
    exit /b 1
)
goto end

:setup-db
echo [INFO] Setting up database...
echo [INFO] Waiting for MongoDB to be ready...
:wait_mongo
docker-compose exec mongodb mongosh --eval "db.adminCommand('ping')" >nul 2>&1
if errorlevel 1 (
    timeout /t 2 /nobreak >nul
    goto wait_mongo
)

if exist "backend\scripts\setup-db.js" (
    echo [INFO] Running database setup script...
    docker-compose exec backend node scripts/setup-db.js
    echo [SUCCESS] Database setup completed!
) else (
    echo [WARNING] No database setup script found.
)
goto end

:cleanup
echo [INFO] Cleaning up Docker resources...
docker-compose down -v
docker image prune -f
docker volume prune -f
docker network prune -f
echo [SUCCESS] Cleanup completed!
goto end

:reset
echo [WARNING] This will remove all containers, volumes, and rebuild everything.
set /p CONFIRM=Are you sure? (y/N): 
if /i "%CONFIRM%"=="y" (
    call :cleanup
    call :build
    call :start
    echo [SUCCESS] Reset completed!
) else (
    echo [INFO] Reset cancelled.
)
goto end

:help
echo RJWU Platform Docker Development Script
echo.
echo Usage: %0 [COMMAND]
echo.
echo Commands:
echo   build       Build all Docker services
echo   start       Start all services
echo   stop        Stop all services
echo   restart     Restart all services
echo   logs        View logs for all services
echo   logs ^<svc^>  View logs for specific service
echo   health      Check health of all services
echo   test        Run tests for all services
echo   test ^<svc^>  Run tests for specific service (backend/frontend)
echo   setup-db    Setup database with initial data
echo   cleanup     Clean up Docker resources
echo   reset       Reset everything (rebuild and restart)
echo   help        Show this help message
echo.
echo Examples:
echo   %0 start
echo   %0 logs backend
echo   %0 test frontend
goto end

:unknown
echo [ERROR] Unknown command: %COMMAND%
call :help
exit /b 1

:end
endlocal
