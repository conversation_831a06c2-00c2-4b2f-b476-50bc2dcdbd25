import React, { useState } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'
import DashboardLayout from '@/components/layouts/DashboardLayout'
import { 
  PlayIcon,
  ClockIcon,
  UserGroupIcon,
  AcademicCapIcon,
  StarIcon,
  CheckIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'

// Mock data - in real app, this would come from API based on course ID
const mockCourse = {
  id: 1,
  title: 'Complete React.js Development Course',
  description: 'Master React.js from basics to advanced concepts including hooks, context, and modern patterns. This comprehensive course will take you from a complete beginner to an advanced React developer.',
  longDescription: `
    This comprehensive React.js course is designed to take you from a complete beginner to an advanced React developer. 
    You'll learn all the fundamental concepts of React including components, props, state, and event handling, 
    then progress to advanced topics like hooks, context API, performance optimization, and testing.

    The course includes hands-on projects where you'll build real-world applications, giving you practical experience 
    that you can showcase in your portfolio. By the end of this course, you'll have the skills and confidence to 
    build modern, scalable React applications.
  `,
  instructor: {
    name: 'Dr. <PERSON>',
    avatar: 'https://via.placeholder.com/80x80',
    bio: 'Senior Software Engineer with 8+ years of experience in React development. Former Facebook engineer.',
    rating: 4.8,
    studentsCount: 15000,
    coursesCount: 12
  },
  thumbnail: 'https://via.placeholder.com/800x450',
  price: 2999,
  originalPrice: 4999,
  rating: 4.7,
  reviewsCount: 1250,
  studentsCount: 1250,
  duration: '40 hours',
  level: 'Intermediate',
  category: 'Web Development',
  language: 'English',
  lastUpdated: '2024-01-10',
  isEnrolled: false,
  isBestseller: true,
  whatYouWillLearn: [
    'Build modern React applications from scratch',
    'Master React Hooks and functional components',
    'Understand state management with Context API and Redux',
    'Implement routing with React Router',
    'Write unit and integration tests',
    'Deploy React applications to production',
    'Optimize React apps for performance',
    'Work with APIs and handle asynchronous operations'
  ],
  requirements: [
    'Basic knowledge of HTML, CSS, and JavaScript',
    'Familiarity with ES6+ features',
    'A computer with internet connection',
    'Code editor (VS Code recommended)'
  ],
  curriculum: [
    {
      id: 1,
      title: 'Getting Started with React',
      duration: '2 hours',
      lessonsCount: 8,
      isExpanded: true,
      lessons: [
        { id: 1, title: 'Introduction to React', duration: '15 min', isCompleted: false, isFree: true },
        { id: 2, title: 'Setting up Development Environment', duration: '20 min', isCompleted: false, isFree: true },
        { id: 3, title: 'Your First React Component', duration: '18 min', isCompleted: false, isFree: false },
        { id: 4, title: 'JSX Fundamentals', duration: '22 min', isCompleted: false, isFree: false },
        { id: 5, title: 'Props and Component Communication', duration: '25 min', isCompleted: false, isFree: false },
        { id: 6, title: 'Handling Events', duration: '20 min', isCompleted: false, isFree: false },
        { id: 7, title: 'Conditional Rendering', duration: '18 min', isCompleted: false, isFree: false },
        { id: 8, title: 'Lists and Keys', duration: '22 min', isCompleted: false, isFree: false }
      ]
    },
    {
      id: 2,
      title: 'State Management and Hooks',
      duration: '6 hours',
      lessonsCount: 12,
      isExpanded: false,
      lessons: []
    },
    {
      id: 3,
      title: 'Advanced React Patterns',
      duration: '8 hours',
      lessonsCount: 15,
      isExpanded: false,
      lessons: []
    },
    {
      id: 4,
      title: 'Testing React Applications',
      duration: '4 hours',
      lessonsCount: 10,
      isExpanded: false,
      lessons: []
    },
    {
      id: 5,
      title: 'Deployment and Production',
      duration: '3 hours',
      lessonsCount: 8,
      isExpanded: false,
      lessons: []
    }
  ],
  reviews: [
    {
      id: 1,
      user: {
        name: 'John Doe',
        avatar: 'https://via.placeholder.com/40x40'
      },
      rating: 5,
      comment: 'Excellent course! Very well structured and easy to follow. The instructor explains concepts clearly.',
      date: '2024-01-05',
      helpful: 24
    },
    {
      id: 2,
      user: {
        name: 'Jane Smith',
        avatar: 'https://via.placeholder.com/40x40'
      },
      rating: 4,
      comment: 'Great content and practical examples. Would recommend to anyone learning React.',
      date: '2024-01-03',
      helpful: 18
    }
  ]
}

export default function CourseDetailPage() {
  const router = useRouter()
  const { id } = router.query
  const [activeTab, setActiveTab] = useState('overview')
  const [expandedSections, setExpandedSections] = useState<number[]>([1])

  const toggleSection = (sectionId: number) => {
    setExpandedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    )
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <StarIconSolid
            key={star}
            className={`h-4 w-4 ${
              star <= Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">{rating}</span>
      </div>
    )
  }

  const handleEnroll = () => {
    // In real app, this would handle enrollment process
    router.push(`/checkout?courseId=${id}`)
  }

  return (
    <>
      <Head>
        <title>{mockCourse.title} - RJWU Platform</title>
        <meta name="description" content={mockCourse.description} />
      </Head>

      <DashboardLayout>
        <div className="max-w-7xl mx-auto">
          {/* Course Header */}
          <div className="bg-gray-900 text-white rounded-lg p-8 mb-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="flex items-center mb-4">
                  {mockCourse.isBestseller && (
                    <span className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded mr-3">
                      Bestseller
                    </span>
                  )}
                  <span className="text-sm text-gray-300">{mockCourse.category}</span>
                </div>
                
                <h1 className="text-3xl font-bold mb-4">{mockCourse.title}</h1>
                <p className="text-lg text-gray-300 mb-6">{mockCourse.description}</p>
                
                <div className="flex items-center space-x-6 text-sm">
                  <div className="flex items-center">
                    {renderStars(mockCourse.rating)}
                    <span className="ml-2">({mockCourse.reviewsCount} reviews)</span>
                  </div>
                  <div className="flex items-center">
                    <UserGroupIcon className="h-4 w-4 mr-1" />
                    {mockCourse.studentsCount} students
                  </div>
                  <div className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    {mockCourse.duration}
                  </div>
                  <div className="flex items-center">
                    <AcademicCapIcon className="h-4 w-4 mr-1" />
                    {mockCourse.level}
                  </div>
                </div>

                <div className="flex items-center mt-4">
                  <img
                    src={mockCourse.instructor.avatar}
                    alt={mockCourse.instructor.name}
                    className="w-10 h-10 rounded-full mr-3"
                  />
                  <div>
                    <p className="font-medium">Created by {mockCourse.instructor.name}</p>
                    <p className="text-sm text-gray-300">Last updated {mockCourse.lastUpdated}</p>
                  </div>
                </div>
              </div>

              <div className="lg:col-span-1">
                <div className="bg-white text-gray-900 rounded-lg p-6">
                  <img
                    src={mockCourse.thumbnail}
                    alt={mockCourse.title}
                    className="w-full rounded-lg mb-4"
                  />
                  
                  <div className="text-center mb-4">
                    <span className="text-3xl font-bold">₹{mockCourse.price}</span>
                    {mockCourse.originalPrice > mockCourse.price && (
                      <span className="ml-2 text-lg text-gray-500 line-through">₹{mockCourse.originalPrice}</span>
                    )}
                  </div>

                  {mockCourse.isEnrolled ? (
                    <button className="w-full bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 mb-4">
                      Continue Learning
                    </button>
                  ) : (
                    <button 
                      onClick={handleEnroll}
                      className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 mb-4"
                    >
                      Enroll Now
                    </button>
                  )}

                  <div className="text-sm text-gray-600 space-y-2">
                    <div className="flex justify-between">
                      <span>Duration:</span>
                      <span>{mockCourse.duration}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Level:</span>
                      <span>{mockCourse.level}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Language:</span>
                      <span>{mockCourse.language}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Students:</span>
                      <span>{mockCourse.studentsCount}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Course Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              {/* Tabs */}
              <div className="border-b border-gray-200 mb-6">
                <nav className="-mb-px flex space-x-8">
                  {[
                    { id: 'overview', name: 'Overview' },
                    { id: 'curriculum', name: 'Curriculum' },
                    { id: 'instructor', name: 'Instructor' },
                    { id: 'reviews', name: 'Reviews' }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {tab.name}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              {activeTab === 'overview' && (
                <div className="space-y-8">
                  <div>
                    <h2 className="text-xl font-semibold mb-4">About this course</h2>
                    <p className="text-gray-700 whitespace-pre-line">{mockCourse.longDescription}</p>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold mb-4">What you'll learn</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {mockCourse.whatYouWillLearn.map((item, index) => (
                        <div key={index} className="flex items-start">
                          <CheckIcon className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{item}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold mb-4">Requirements</h2>
                    <ul className="space-y-2">
                      {mockCourse.requirements.map((requirement, index) => (
                        <li key={index} className="flex items-start">
                          <span className="w-2 h-2 bg-gray-400 rounded-full mr-3 mt-2 flex-shrink-0" />
                          <span className="text-gray-700">{requirement}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {activeTab === 'curriculum' && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Course Curriculum</h2>
                  <div className="space-y-4">
                    {mockCourse.curriculum.map((section) => (
                      <div key={section.id} className="border border-gray-200 rounded-lg">
                        <button
                          onClick={() => toggleSection(section.id)}
                          className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
                        >
                          <div>
                            <h3 className="font-medium text-gray-900">{section.title}</h3>
                            <p className="text-sm text-gray-500">
                              {section.lessonsCount} lessons • {section.duration}
                            </p>
                          </div>
                          {expandedSections.includes(section.id) ? (
                            <ChevronDownIcon className="h-5 w-5 text-gray-400" />
                          ) : (
                            <ChevronRightIcon className="h-5 w-5 text-gray-400" />
                          )}
                        </button>
                        
                        {expandedSections.includes(section.id) && section.lessons.length > 0 && (
                          <div className="border-t border-gray-200">
                            {section.lessons.map((lesson) => (
                              <div key={lesson.id} className="flex items-center justify-between p-4 border-b border-gray-100 last:border-b-0">
                                <div className="flex items-center">
                                  <PlayIcon className="h-4 w-4 text-gray-400 mr-3" />
                                  <span className="text-sm text-gray-700">{lesson.title}</span>
                                  {lesson.isFree && (
                                    <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                                      Free
                                    </span>
                                  )}
                                </div>
                                <span className="text-sm text-gray-500">{lesson.duration}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'instructor' && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">About the Instructor</h2>
                  <div className="flex items-start space-x-4">
                    <img
                      src={mockCourse.instructor.avatar}
                      alt={mockCourse.instructor.name}
                      className="w-20 h-20 rounded-full"
                    />
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{mockCourse.instructor.name}</h3>
                      <p className="text-gray-600 mb-2">{mockCourse.instructor.bio}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          {renderStars(mockCourse.instructor.rating)}
                        </div>
                        <span>{mockCourse.instructor.studentsCount.toLocaleString()} students</span>
                        <span>{mockCourse.instructor.coursesCount} courses</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'reviews' && (
                <div>
                  <h2 className="text-xl font-semibold mb-4">Student Reviews</h2>
                  <div className="space-y-6">
                    {mockCourse.reviews.map((review) => (
                      <div key={review.id} className="border-b border-gray-200 pb-6">
                        <div className="flex items-start space-x-4">
                          <img
                            src={review.user.avatar}
                            alt={review.user.name}
                            className="w-10 h-10 rounded-full"
                          />
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium text-gray-900">{review.user.name}</h4>
                              <span className="text-sm text-gray-500">{review.date}</span>
                            </div>
                            {renderStars(review.rating)}
                            <p className="text-gray-700 mt-2">{review.comment}</p>
                            <button className="text-sm text-gray-500 hover:text-gray-700 mt-2">
                              Helpful ({review.helpful})
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-6">
                <div className="bg-white rounded-lg shadow p-6 mb-6">
                  <h3 className="font-semibold text-gray-900 mb-4">Course Features</h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Duration</span>
                      <span className="font-medium">{mockCourse.duration}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Skill Level</span>
                      <span className="font-medium">{mockCourse.level}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Language</span>
                      <span className="font-medium">{mockCourse.language}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Certificate</span>
                      <span className="font-medium">Yes</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Lifetime Access</span>
                      <span className="font-medium">Yes</span>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="font-semibold text-gray-900 mb-4">Share this course</h3>
                  <div className="flex space-x-2">
                    <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded text-sm hover:bg-blue-700">
                      Facebook
                    </button>
                    <button className="flex-1 bg-blue-400 text-white py-2 px-4 rounded text-sm hover:bg-blue-500">
                      Twitter
                    </button>
                    <button className="flex-1 bg-blue-700 text-white py-2 px-4 rounded text-sm hover:bg-blue-800">
                      LinkedIn
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </>
  )
}
