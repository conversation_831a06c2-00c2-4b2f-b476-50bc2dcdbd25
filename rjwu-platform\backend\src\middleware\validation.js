const { body, param, query, validationResult } = require('express-validator');
const { AppError } = require('./errorHandler');
const logger = require('../utils/logger');

/**
 * Validation Middleware
 * Provides request validation using express-validator
 */

// Common validation patterns
const patterns = {
  objectId: /^[0-9a-fA-F]{24}$/,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^(\+91|91)?[6-9]\d{9}$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
};

/**
 * Handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const validationErrors = {};
    errors.array().forEach(error => {
      if (!validationErrors[error.path]) {
        validationErrors[error.path] = [];
      }
      validationErrors[error.path].push(error.msg);
    });

    logger.warn('Validation failed:', { errors: validationErrors });
    return next(new AppError('Validation failed', 400, validationErrors));
  }
  next();
};

// User validation rules
const userValidationRules = {
  register: [
    body('firstName').trim().isLength({ min: 2, max: 50 }).withMessage('First name must be between 2 and 50 characters'),
    body('lastName').trim().isLength({ min: 2, max: 50 }).withMessage('Last name must be between 2 and 50 characters'),
    body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
    body('phone').matches(patterns.phone).withMessage('Please provide a valid Indian phone number'),
    body('password').matches(patterns.password).withMessage('Password must be at least 8 characters with uppercase, lowercase, and number'),
    body('role').optional().isIn(['student', 'instructor']).withMessage('Role must be student or instructor'),
    body('dateOfBirth').optional().isISO8601().toDate().withMessage('Please provide a valid date'),
    body('gender').optional().isIn(['male', 'female', 'other', 'prefer_not_to_say']).withMessage('Invalid gender value')
  ],

  login: [
    body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
    body('password').notEmpty().withMessage('Password is required')
  ],

  updateProfile: [
    body('firstName').optional().trim().isLength({ min: 2, max: 50 }).withMessage('First name must be between 2 and 50 characters'),
    body('lastName').optional().trim().isLength({ min: 2, max: 50 }).withMessage('Last name must be between 2 and 50 characters'),
    body('phone').optional().matches(patterns.phone).withMessage('Please provide a valid Indian phone number'),
    body('dateOfBirth').optional().isISO8601().toDate().withMessage('Please provide a valid date'),
    body('gender').optional().isIn(['male', 'female', 'other', 'prefer_not_to_say']).withMessage('Invalid gender value'),
    body('bio').optional().isLength({ max: 500 }).withMessage('Bio must not exceed 500 characters')
  ],

  changePassword: [
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    body('newPassword').matches(patterns.password).withMessage('New password must be at least 8 characters with uppercase, lowercase, and number')
  ]
};

// Course validation rules
const courseValidationRules = {
  create: [
    body('title').trim().isLength({ min: 5, max: 200 }).withMessage('Title must be between 5 and 200 characters'),
    body('description').trim().isLength({ min: 20, max: 5000 }).withMessage('Description must be between 20 and 5000 characters'),
    body('longDescription').optional().isLength({ max: 10000 }).withMessage('Long description must not exceed 10000 characters'),
    body('category').trim().isLength({ min: 2, max: 100 }).withMessage('Category must be between 2 and 100 characters'),
    body('level').isIn(['beginner', 'intermediate', 'advanced']).withMessage('Level must be beginner, intermediate, or advanced'),
    body('price').isFloat({ min: 0, max: 100000 }).withMessage('Price must be between 0 and 100000'),
    body('originalPrice').optional().isFloat({ min: 0, max: 100000 }).withMessage('Original price must be between 0 and 100000'),
    body('duration').isInt({ min: 1, max: 1000 }).withMessage('Duration must be between 1 and 1000 hours'),
    body('language').optional().isIn(['english', 'hindi', 'bengali', 'tamil', 'telugu', 'marathi', 'gujarati', 'kannada', 'malayalam', 'punjabi']).withMessage('Invalid language'),
    body('thumbnail').optional().isURL().withMessage('Thumbnail must be a valid URL'),
    body('prerequisites').optional().isArray().withMessage('Prerequisites must be an array'),
    body('learningOutcomes').optional().isArray().withMessage('Learning outcomes must be an array')
  ],

  update: [
    body('title').optional().trim().isLength({ min: 5, max: 200 }).withMessage('Title must be between 5 and 200 characters'),
    body('description').optional().trim().isLength({ min: 20, max: 5000 }).withMessage('Description must be between 20 and 5000 characters'),
    body('longDescription').optional().isLength({ max: 10000 }).withMessage('Long description must not exceed 10000 characters'),
    body('category').optional().trim().isLength({ min: 2, max: 100 }).withMessage('Category must be between 2 and 100 characters'),
    body('level').optional().isIn(['beginner', 'intermediate', 'advanced']).withMessage('Level must be beginner, intermediate, or advanced'),
    body('price').optional().isFloat({ min: 0, max: 100000 }).withMessage('Price must be between 0 and 100000'),
    body('originalPrice').optional().isFloat({ min: 0, max: 100000 }).withMessage('Original price must be between 0 and 100000'),
    body('duration').optional().isInt({ min: 1, max: 1000 }).withMessage('Duration must be between 1 and 1000 hours'),
    body('language').optional().isIn(['english', 'hindi', 'bengali', 'tamil', 'telugu', 'marathi', 'gujarati', 'kannada', 'malayalam', 'punjabi']).withMessage('Invalid language'),
    body('thumbnail').optional().isURL().withMessage('Thumbnail must be a valid URL'),
    body('status').optional().isIn(['draft', 'published', 'archived']).withMessage('Status must be draft, published, or archived')
  ],

  addReview: [
    body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
    body('comment').trim().isLength({ min: 10, max: 1000 }).withMessage('Comment must be between 10 and 1000 characters')
  ]
};

// Payment validation rules
const paymentValidationRules = {
  createOrder: [
    body('course').matches(patterns.objectId).withMessage('Invalid course ID'),
    body('amount').isFloat({ min: 0, max: 100000 }).withMessage('Amount must be between 0 and 100000'),
    body('currency').optional().isIn(['INR', 'USD']).withMessage('Currency must be INR or USD'),
    body('gateway').isIn(['razorpay', 'stripe', 'payu']).withMessage('Invalid payment gateway')
  ],

  verifyPayment: [
    body('orderId').notEmpty().withMessage('Order ID is required'),
    body('paymentId').notEmpty().withMessage('Payment ID is required'),
    body('signature').notEmpty().withMessage('Signature is required'),
    body('gateway').isIn(['razorpay', 'stripe', 'payu']).withMessage('Invalid payment gateway')
  ]
};

// Common validation rules
const commonValidationRules = {
  objectId: [
    param('id').matches(patterns.objectId).withMessage('Invalid ID format')
  ],

  pagination: [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('sort').optional().isString().withMessage('Sort must be a string'),
    query('order').optional().isIn(['asc', 'desc']).withMessage('Order must be asc or desc'),
    query('search').optional().isLength({ min: 2, max: 100 }).withMessage('Search term must be between 2 and 100 characters')
  ]
};

/**
 * Validate file upload parameters
 * @returns {Function} Express middleware function
 */
const validateFileUpload = () => {
  return (req, res, next) => {
    try {
      if (!req.file && !req.files) {
        return next(new AppError('No file uploaded', 400));
      }

      const file = req.file || (req.files && req.files[0]);

      // Validate file size (10MB limit)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        return next(new AppError('File size exceeds 10MB limit', 400));
      }

      // Validate file type based on upload type
      const allowedTypes = {
        avatar: ['image/jpeg', 'image/png', 'image/gif'],
        course_thumbnail: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        document: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        video: ['video/mp4', 'video/avi', 'video/mov', 'video/wmv']
      };

      const uploadType = req.body.type || 'document';
      const allowed = allowedTypes[uploadType] || allowedTypes.document;

      if (!allowed.includes(file.mimetype)) {
        return next(new AppError(`Invalid file type for ${uploadType}. Allowed types: ${allowed.join(', ')}`, 400));
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// Export validation middleware
module.exports = {
  // Core validation handler
  handleValidationErrors,

  // User validations
  validateUserRegistration: [...userValidationRules.register, handleValidationErrors],
  validateUserLogin: [...userValidationRules.login, handleValidationErrors],
  validateUserUpdate: [...userValidationRules.updateProfile, handleValidationErrors],
  validatePasswordChange: [...userValidationRules.changePassword, handleValidationErrors],

  // Course validations
  validateCourseCreation: [...courseValidationRules.create, handleValidationErrors],
  validateCourseUpdate: [...courseValidationRules.update, handleValidationErrors],
  validateCourseReview: [...courseValidationRules.addReview, handleValidationErrors],

  // Payment validations
  validatePaymentOrder: [...paymentValidationRules.createOrder, handleValidationErrors],
  validatePaymentVerification: [...paymentValidationRules.verifyPayment, handleValidationErrors],

  // Common validations
  validateObjectId: [...commonValidationRules.objectId, handleValidationErrors],
  validatePagination: [...commonValidationRules.pagination, handleValidationErrors],
  validateFileUpload
};
