# @redis/bloom

This package provides support for the [RedisBloom](https://redisbloom.io) module, which adds additional probabilistic data structures to Redis.  It extends the [Node Redis client](https://github.com/redis/node-redis) to include functions for each of the RediBloom commands.

To use these extra commands, your Redis server must have the RedisBloom module installed.

RedisBloom provides the following probabilistic data structures:

* Bloom Filter: for checking set membership with a high degree of certainty.
* Cuckoo Filter: for checking set membership with a high degree of certainty.
* Count-Min Sketch: Determine the frequency of events in a stream.
* Top-K: Maintain a list of k most frequently seen items.

For complete examples, see `bloom-filter.js`, `cuckoo-filter.js`, `count-min-sketch.js` and `topk.js` in the Node Redis examples folder.
