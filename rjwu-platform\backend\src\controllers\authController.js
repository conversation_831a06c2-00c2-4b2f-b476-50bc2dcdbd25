const crypto = require('crypto');
const { promisify } = require('util');
const jwt = require('jsonwebtoken');
const UserService = require('../services/UserService');
const { AppError } = require('../middleware/errorHandler');
const { createSendToken } = require('../middleware/auth');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');
const sendEmail = require('../utils/sendEmail');
const config = require('../config/config');

// Register new user
const register = async (req, res, next) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed: ' + errors.array().map(e => e.msg).join(', '), 400));
    }

    const { firstName, lastName, email, phone, password, role = 'student' } = req.body;

    // Create new user using UserService
    const newUser = await UserService.createUser({
      firstName,
      lastName,
      email,
      phone,
      password,
      role
    });

    // Generate email verification token
    const emailToken = crypto.randomBytes(32).toString('hex');
    newUser.emailVerificationToken = crypto
      .createHash('sha256')
      .update(emailToken)
      .digest('hex');
    newUser.emailVerificationExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 hours

    await newUser.save({ validateBeforeSave: false });

    // Send verification email
    try {
      const verifyURL = `${req.protocol}://${req.get('host')}/api/auth/verify-email/${emailToken}`;
      
      await sendEmail({
        email: newUser.email,
        subject: 'Verify your email address - RJWU Platform',
        message: `Please click the following link to verify your email: ${verifyURL}`
      });

      logger.info(`Email verification sent to ${newUser.email}`);
    } catch (err) {
      logger.error('Error sending verification email:', err);
      newUser.emailVerificationToken = undefined;
      newUser.emailVerificationExpires = undefined;
      await newUser.save({ validateBeforeSave: false });
    }

    logger.info(`New user registered: ${email}`);
    
    createSendToken(newUser, 201, res, 'User registered successfully. Please check your email for verification.');
  } catch (error) {
    logger.error('Registration error:', error);
    next(error);
  }
};

// Login user
const login = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed: ' + errors.array().map(e => e.msg).join(', '), 400));
    }

    const { email, password } = req.body;

    // Find user and include password
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      return next(new AppError('Invalid email or password', 401));
    }

    // Check if account is locked
    if (user.isLocked) {
      return next(new AppError('Account temporarily locked due to too many failed login attempts', 423));
    }

    // Check password
    const isPasswordCorrect = await user.comparePassword(password);

    if (!isPasswordCorrect) {
      // Increment login attempts
      await user.incLoginAttempts();
      return next(new AppError('Invalid email or password', 401));
    }

    // Check if user is active
    if (!user.isActive) {
      return next(new AppError('Your account has been deactivated', 401));
    }

    // Reset login attempts on successful login
    if (user.loginAttempts && user.loginAttempts > 0) {
      await user.resetLoginAttempts();
    }

    // Update last login date and login streak
    const today = new Date();
    const lastLogin = user.learningStats.lastLoginDate;
    
    if (lastLogin) {
      const diffTime = Math.abs(today - lastLogin);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 1) {
        user.learningStats.loginStreak += 1;
      } else if (diffDays > 1) {
        user.learningStats.loginStreak = 1;
      }
    } else {
      user.learningStats.loginStreak = 1;
    }
    
    user.learningStats.lastLoginDate = today;
    await user.save({ validateBeforeSave: false });

    logger.info(`User logged in: ${email}`);
    
    createSendToken(user, 200, res, 'Logged in successfully');
  } catch (error) {
    logger.error('Login error:', error);
    next(error);
  }
};

// Logout user
const logout = (req, res) => {
  res.cookie('jwt', 'loggedout', {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true
  });
  
  logger.info(`User logged out: ${req.user ? req.user.email : 'unknown'}`);
  
  res.status(200).json({
    status: 'success',
    message: 'Logged out successfully'
  });
};

// Forgot password
const forgotPassword = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed: ' + errors.array().map(e => e.msg).join(', '), 400));
    }

    // Get user based on posted email
    const user = await User.findOne({ email: req.body.email });
    if (!user) {
      return next(new AppError('No user found with that email address', 404));
    }

    // Generate random reset token
    const resetToken = crypto.randomBytes(32).toString('hex');

    user.passwordResetToken = crypto
      .createHash('sha256')
      .update(resetToken)
      .digest('hex');
    user.passwordResetExpires = Date.now() + 10 * 60 * 1000; // 10 minutes

    await user.save({ validateBeforeSave: false });

    // Send reset email
    try {
      const resetURL = `${req.protocol}://${req.get('host')}/api/auth/reset-password/${resetToken}`;

      await sendEmail({
        email: user.email,
        subject: 'Password Reset Request - RJWU Platform',
        message: `Forgot your password? Click the following link to reset it: ${resetURL}\n\nThis link will expire in 10 minutes.\n\nIf you didn't request this, please ignore this email.`
      });

      logger.info(`Password reset email sent to ${user.email}`);

      res.status(200).json({
        status: 'success',
        message: 'Password reset email sent successfully'
      });
    } catch (err) {
      logger.error('Error sending password reset email:', err);
      user.passwordResetToken = undefined;
      user.passwordResetExpires = undefined;
      await user.save({ validateBeforeSave: false });

      return next(new AppError('Error sending email. Please try again later.', 500));
    }
  } catch (error) {
    logger.error('Forgot password error:', error);
    next(error);
  }
};

// Reset password
const resetPassword = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed: ' + errors.array().map(e => e.msg).join(', '), 400));
    }

    // Get user based on token
    const hashedToken = crypto
      .createHash('sha256')
      .update(req.params.token)
      .digest('hex');

    const user = await User.findOne({
      passwordResetToken: hashedToken,
      passwordResetExpires: { $gt: Date.now() }
    });

    if (!user) {
      return next(new AppError('Token is invalid or has expired', 400));
    }

    // Set new password
    user.password = req.body.password;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    
    await user.save();

    logger.info(`Password reset successful for user: ${user.email}`);

    // Log user in
    createSendToken(user, 200, res, 'Password reset successful');
  } catch (error) {
    logger.error('Reset password error:', error);
    next(error);
  }
};

// Verify email
const verifyEmail = async (req, res, next) => {
  try {
    const hashedToken = crypto
      .createHash('sha256')
      .update(req.params.token)
      .digest('hex');

    const user = await User.findOne({
      emailVerificationToken: hashedToken,
      emailVerificationExpires: { $gt: Date.now() }
    });

    if (!user) {
      return next(new AppError('Token is invalid or has expired', 400));
    }

    // Update user verification status
    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    
    await user.save({ validateBeforeSave: false });

    logger.info(`Email verified for user: ${user.email}`);

    res.status(200).json({
      status: 'success',
      message: 'Email verified successfully'
    });
  } catch (error) {
    logger.error('Email verification error:', error);
    next(error);
  }
};

// Resend verification email
const resendVerificationEmail = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return next(new AppError('User not found', 404));
    }

    if (user.isEmailVerified) {
      return next(new AppError('Email is already verified', 400));
    }

    // Generate new verification token
    const emailToken = crypto.randomBytes(32).toString('hex');
    user.emailVerificationToken = crypto
      .createHash('sha256')
      .update(emailToken)
      .digest('hex');
    user.emailVerificationExpires = Date.now() + 24 * 60 * 60 * 1000; // 24 hours

    await user.save({ validateBeforeSave: false });

    // Send verification email
    try {
      const verifyURL = `${req.protocol}://${req.get('host')}/api/auth/verify-email/${emailToken}`;
      
      await sendEmail({
        email: user.email,
        subject: 'Verify your email address - RJWU Platform',
        message: `Please click the following link to verify your email: ${verifyURL}`
      });

      logger.info(`Verification email resent to ${user.email}`);

      res.status(200).json({
        status: 'success',
        message: 'Verification email sent successfully'
      });
    } catch (err) {
      logger.error('Error resending verification email:', err);
      user.emailVerificationToken = undefined;
      user.emailVerificationExpires = undefined;
      await user.save({ validateBeforeSave: false });

      return next(new AppError('Error sending email. Please try again later.', 500));
    }
  } catch (error) {
    logger.error('Resend verification email error:', error);
    next(error);
  }
};

// Change password (for logged in users)
const changePassword = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed: ' + errors.array().map(e => e.msg).join(', '), 400));
    }

    // Get user from collection
    const user = await User.findById(req.user.id).select('+password');

    // Check if current password is correct
    const isCurrentPasswordCorrect = await user.comparePassword(req.body.currentPassword);
    
    if (!isCurrentPasswordCorrect) {
      return next(new AppError('Current password is incorrect', 401));
    }

    // Update password
    user.password = req.body.newPassword;
    await user.save();

    logger.info(`Password changed for user: ${user.email}`);

    // Send updated token
    createSendToken(user, 200, res, 'Password changed successfully');
  } catch (error) {
    logger.error('Change password error:', error);
    next(error);
  }
};

module.exports = {
  register,
  login,
  logout,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerificationEmail,
  changePassword
};
