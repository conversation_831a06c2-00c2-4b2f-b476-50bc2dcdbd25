const BatchService = require('../services/BatchService');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

// Get all batches with filtering and pagination
const getAllBatches = async (req, res, next) => {
  try {
    // Build filters from query parameters
    const filters = {};
    if (req.query.status) filters.status = req.query.status;
    if (req.query.mode) filters.mode = req.query.mode;
    if (req.query.course) filters.course = req.query.course;
    if (req.query.instructor) filters.instructor = req.query.instructor;

    // Date filters
    if (req.query.startDate) {
      filters.startDate = { $gte: new Date(req.query.startDate) };
    }
    if (req.query.endDate) {
      filters.endDate = { $lte: new Date(req.query.endDate) };
    }

    // Options for pagination and sorting
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 10,
      sort: req.query.sortBy ? req.query.sortBy.split(':')[0] : 'startDate',
      order: req.query.sortBy ? req.query.sortBy.split(':')[1] : 'asc',
      search: req.query.search
    };

    const result = await BatchService.getBatches(filters, options);

    res.status(200).json({
      status: 'success',
      results: result.batches.length,
      pagination: result.pagination,
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        batches
      }
    });
  } catch (error) {
    logger.error('Get all batches error:', error);
    next(error);
  }
};

// Get single batch details
const getBatch = async (req, res, next) => {
  try {
    const batch = await Batch.findById(req.params.id)
      .populate('course', 'title description thumbnail curriculum')
      .populate('instructor', 'firstName lastName email instructorProfile')
      .populate('students.student', 'firstName lastName email avatar phone');

    if (!batch) {
      return next(new AppError('Batch not found', 404));
    }

    if (!batch.isActive && req.user.role !== 'admin') {
      return next(new AppError('Batch not available', 404));
    }

    // Check if user has access to this batch
    let hasAccess = false;
    if (req.user.role === 'admin' || 
        batch.instructor._id.toString() === req.user.id ||
        batch.students.some(s => s.student._id.toString() === req.user.id)) {
      hasAccess = true;
    }

    // If user doesn't have access, hide sensitive information
    if (!hasAccess) {
      batch.students = [];
      batch.schedule = batch.schedule.map(session => ({
        date: session.date,
        startTime: session.startTime,
        endTime: session.endTime,
        topic: session.topic
      }));
    }

    res.status(200).json({
      status: 'success',
      data: {
        batch,
        hasAccess
      }
    });
  } catch (error) {
    logger.error('Get batch error:', error);
    next(error);
  }
};

// Create new batch (instructor/admin only)
const createBatch = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed: ' + errors.array().map(e => e.msg).join(', '), 400));
    }

    // Verify course exists
    const course = await Course.findById(req.body.course);
    if (!course) {
      return next(new AppError('Course not found', 404));
    }

    // Set instructor (only admin can assign different instructor)
    if (req.user.role === 'admin' && req.body.instructor) {
      // Admin can assign any instructor
    } else {
      req.body.instructor = req.user.id;
    }

    // Verify instructor exists and has instructor role
    const instructor = await User.findById(req.body.instructor);
    if (!instructor || instructor.role !== 'instructor') {
      return next(new AppError('Invalid instructor', 400));
    }

    const batch = await Batch.create(req.body);

    await batch.populate([
      { path: 'course', select: 'title description' },
      { path: 'instructor', select: 'firstName lastName email' }
    ]);

    logger.info(`New batch created: ${batch.name} by ${req.user.email}`);

    res.status(201).json({
      status: 'success',
      message: 'Batch created successfully',
      data: {
        batch
      }
    });
  } catch (error) {
    logger.error('Create batch error:', error);
    next(error);
  }
};

// Update batch
const updateBatch = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed: ' + errors.array().map(e => e.msg).join(', '), 400));
    }

    const batch = await Batch.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    ).populate([
      { path: 'course', select: 'title description' },
      { path: 'instructor', select: 'firstName lastName email' }
    ]);

    if (!batch) {
      return next(new AppError('Batch not found', 404));
    }

    logger.info(`Batch updated: ${batch.name} by ${req.user.email}`);

    res.status(200).json({
      status: 'success',
      message: 'Batch updated successfully',
      data: {
        batch
      }
    });
  } catch (error) {
    logger.error('Update batch error:', error);
    next(error);
  }
};

// Delete batch
const deleteBatch = async (req, res, next) => {
  try {
    const batch = await Batch.findById(req.params.id);

    if (!batch) {
      return next(new AppError('Batch not found', 404));
    }

    // Check if batch has enrolled students
    if (batch.students.length > 0 && batch.status === 'active') {
      return next(new AppError('Cannot delete batch with enrolled students', 400));
    }

    // Soft delete
    batch.isActive = false;
    await batch.save();

    logger.info(`Batch deleted: ${batch.name} by ${req.user.email}`);

    res.status(200).json({
      status: 'success',
      message: 'Batch deleted successfully'
    });
  } catch (error) {
    logger.error('Delete batch error:', error);
    next(error);
  }
};

// Enroll student in batch
const enrollStudent = async (req, res, next) => {
  try {
    const { studentId } = req.body;
    const batch = await Batch.findById(req.params.id);

    if (!batch) {
      return next(new AppError('Batch not found', 404));
    }

    if (batch.status !== 'upcoming' && batch.status !== 'active') {
      return next(new AppError('Cannot enroll in this batch', 400));
    }

    // Check if batch is full
    if (batch.students.length >= batch.maxStudents) {
      return next(new AppError('Batch is full', 400));
    }

    // Check if student already enrolled
    const isAlreadyEnrolled = batch.students.some(
      s => s.student.toString() === (studentId || req.user.id)
    );

    if (isAlreadyEnrolled) {
      return next(new AppError('Student already enrolled in this batch', 400));
    }

    // Verify student exists
    const student = await User.findById(studentId || req.user.id);
    if (!student) {
      return next(new AppError('Student not found', 404));
    }

    // Add student to batch
    batch.students.push({
      student: student._id,
      enrollmentDate: new Date(),
      status: 'active'
    });

    await batch.save();

    logger.info(`Student enrolled in batch: ${student.email} -> ${batch.name}`);

    res.status(200).json({
      status: 'success',
      message: 'Successfully enrolled in batch',
      data: {
        enrollment: batch.students[batch.students.length - 1]
      }
    });
  } catch (error) {
    logger.error('Enroll student error:', error);
    next(error);
  }
};

// Remove student from batch
const removeStudent = async (req, res, next) => {
  try {
    const { studentId } = req.params;
    const batch = await Batch.findById(req.params.id);

    if (!batch) {
      return next(new AppError('Batch not found', 404));
    }

    // Find and remove student
    const studentIndex = batch.students.findIndex(
      s => s.student.toString() === studentId
    );

    if (studentIndex === -1) {
      return next(new AppError('Student not found in this batch', 404));
    }

    batch.students.splice(studentIndex, 1);
    await batch.save();

    logger.info(`Student removed from batch: ${studentId} from ${batch.name}`);

    res.status(200).json({
      status: 'success',
      message: 'Student removed from batch successfully'
    });
  } catch (error) {
    logger.error('Remove student error:', error);
    next(error);
  }
};

// Get batch schedule
const getBatchSchedule = async (req, res, next) => {
  try {
    const batch = await Batch.findById(req.params.id)
      .populate('course', 'title')
      .populate('instructor', 'firstName lastName')
      .select('name schedule course instructor mode');

    if (!batch) {
      return next(new AppError('Batch not found', 404));
    }

    // Check access
    const hasAccess = req.user.role === 'admin' || 
                     batch.instructor._id.toString() === req.user.id ||
                     batch.students.some(s => s.student.toString() === req.user.id);

    if (!hasAccess) {
      return next(new AppError('Access denied', 403));
    }

    res.status(200).json({
      status: 'success',
      data: {
        schedule: batch.schedule,
        batchInfo: {
          name: batch.name,
          course: batch.course.title,
          instructor: `${batch.instructor.firstName} ${batch.instructor.lastName}`,
          mode: batch.mode
        }
      }
    });
  } catch (error) {
    logger.error('Get batch schedule error:', error);
    next(error);
  }
};

// Update batch schedule
const updateBatchSchedule = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed: ' + errors.array().map(e => e.msg).join(', '), 400));
    }

    const batch = await Batch.findById(req.params.id);

    if (!batch) {
      return next(new AppError('Batch not found', 404));
    }

    batch.schedule = req.body.schedule;
    await batch.save();

    logger.info(`Batch schedule updated: ${batch.name} by ${req.user.email}`);

    res.status(200).json({
      status: 'success',
      message: 'Batch schedule updated successfully',
      data: {
        schedule: batch.schedule
      }
    });
  } catch (error) {
    logger.error('Update batch schedule error:', error);
    next(error);
  }
};

// Get instructor's batches
const getInstructorBatches = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = { instructor: req.user.id };
    if (req.query.status) filter.status = req.query.status;

    const batches = await Batch.find(filter)
      .populate('course', 'title description')
      .populate('students.student', 'firstName lastName email')
      .sort({ startDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Batch.countDocuments(filter);

    res.status(200).json({
      status: 'success',
      results: batches.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        batches
      }
    });
  } catch (error) {
    logger.error('Get instructor batches error:', error);
    next(error);
  }
};

// Get student's batches
const getStudentBatches = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = {
      'students.student': req.user.id,
      isActive: true
    };

    if (req.query.status) filter.status = req.query.status;

    const batches = await Batch.find(filter)
      .populate('course', 'title description thumbnail')
      .populate('instructor', 'firstName lastName instructorProfile.rating')
      .sort({ startDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Batch.countDocuments(filter);

    res.status(200).json({
      status: 'success',
      results: batches.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        batches
      }
    });
  } catch (error) {
    logger.error('Get student batches error:', error);
    next(error);
  }
};

module.exports = {
  getAllBatches,
  getBatch,
  createBatch,
  updateBatch,
  deleteBatch,
  enrollStudent,
  removeStudent,
  getBatchSchedule,
  updateBatchSchedule,
  getInstructorBatches,
  getStudentBatches
};
