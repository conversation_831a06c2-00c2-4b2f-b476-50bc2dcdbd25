const express = require('express');
const mongoose = require('mongoose');
const redis = require('../config/redis');

const router = express.Router();

/**
 * Health check endpoint
 * Returns the health status of the application and its dependencies
 */
router.get('/health', async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      services: {}
    };

    // Check MongoDB connection
    try {
      const mongoState = mongoose.connection.readyState;
      health.services.mongodb = {
        status: mongoState === 1 ? 'healthy' : 'unhealthy',
        readyState: mongoState,
        message: getMongoStateMessage(mongoState)
      };
    } catch (error) {
      health.services.mongodb = {
        status: 'unhealthy',
        error: error.message
      };
    }

    // Check Redis connection
    try {
      if (redis && redis.ping) {
        await redis.ping();
        health.services.redis = {
          status: 'healthy',
          message: 'Connected'
        };
      } else {
        health.services.redis = {
          status: 'unhealthy',
          message: 'Redis client not available'
        };
      }
    } catch (error) {
      health.services.redis = {
        status: 'unhealthy',
        error: error.message
      };
    }

    // Check memory usage
    const memUsage = process.memoryUsage();
    health.memory = {
      rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
      heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
      external: `${Math.round(memUsage.external / 1024 / 1024)}MB`
    };

    // Determine overall health status
    const unhealthyServices = Object.values(health.services).filter(
      service => service.status === 'unhealthy'
    );

    if (unhealthyServices.length > 0) {
      health.status = 'degraded';
      return res.status(503).json(health);
    }

    res.status(200).json(health);
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

/**
 * Readiness check endpoint
 * Returns 200 when the application is ready to serve requests
 */
router.get('/ready', async (req, res) => {
  try {
    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      return res.status(503).json({
        status: 'not ready',
        message: 'Database not connected'
      });
    }

    // Check if Redis is connected (if configured)
    if (redis && redis.ping) {
      try {
        await redis.ping();
      } catch (error) {
        return res.status(503).json({
          status: 'not ready',
          message: 'Redis not connected'
        });
      }
    }

    res.status(200).json({
      status: 'ready',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      error: error.message
    });
  }
});

/**
 * Liveness check endpoint
 * Returns 200 if the application is alive
 */
router.get('/live', (req, res) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

/**
 * Get MongoDB connection state message
 */
function getMongoStateMessage(state) {
  const states = {
    0: 'Disconnected',
    1: 'Connected',
    2: 'Connecting',
    3: 'Disconnecting',
    4: 'Invalid credentials',
    99: 'Uninitialized'
  };
  return states[state] || 'Unknown';
}

module.exports = router;
