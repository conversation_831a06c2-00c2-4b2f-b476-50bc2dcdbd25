const BaseService = require('./BaseService');
const User = require('../models/User');
const EmailService = require('./EmailService');

/**
 * Notification Service
 * Handles all notification-related operations including in-app, email, and push notifications
 */
class NotificationService extends BaseService {
  constructor() {
    super('NotificationService');
    this.emailService = EmailService;
  }

  /**
   * Send notification to user(s)
   * @param {Object} notificationData - Notification data
   * @param {string|Array} notificationData.recipients - User ID(s) or email(s)
   * @param {string} notificationData.title - Notification title
   * @param {string} notificationData.message - Notification message
   * @param {string} notificationData.type - Notification type (info, success, warning, error)
   * @param {Array} notificationData.channels - Notification channels (in-app, email, push)
   * @param {Object} notificationData.data - Additional data
   */
  async sendNotification({
    recipients,
    title,
    message,
    type = 'info',
    channels = ['in-app'],
    data = {}
  }) {
    try {
      this.validateRequired({ recipients, title, message }, ['recipients', 'title', 'message']);

      const recipientList = Array.isArray(recipients) ? recipients : [recipients];
      const results = [];

      for (const recipient of recipientList) {
        const result = await this.sendToRecipient({
          recipient,
          title,
          message,
          type,
          channels,
          data
        });
        results.push(result);
      }

      this.log('info', 'Notifications sent successfully', {
        recipientCount: recipientList.length,
        channels,
        type
      });

      return this.formatResponse(results, 'Notifications sent successfully');
    } catch (error) {
      this.handleError(error, 'Send notification');
    }
  }

  /**
   * Send notification to a single recipient
   */
  async sendToRecipient({ recipient, title, message, type, channels, data }) {
    try {
      let user;
      
      // Get user by ID or email
      if (recipient.includes('@')) {
        user = await User.findOne({ email: recipient });
      } else {
        user = await User.findById(recipient);
      }

      if (!user) {
        throw new Error(`User not found: ${recipient}`);
      }

      const results = {};

      // Send in-app notification
      if (channels.includes('in-app')) {
        results.inApp = await this.sendInAppNotification(user, {
          title,
          message,
          type,
          data
        });
      }

      // Send email notification
      if (channels.includes('email')) {
        results.email = await this.sendEmailNotification(user, {
          title,
          message,
          type,
          data
        });
      }

      // Send push notification
      if (channels.includes('push')) {
        results.push = await this.sendPushNotification(user, {
          title,
          message,
          type,
          data
        });
      }

      return {
        recipient: user._id,
        email: user.email,
        results
      };
    } catch (error) {
      this.log('error', `Failed to send notification to ${recipient}`, { error: error.message });
      return {
        recipient,
        error: error.message
      };
    }
  }

  /**
   * Send in-app notification
   */
  async sendInAppNotification(user, { title, message, type, data }) {
    try {
      // Add notification to user's notifications array
      const notification = {
        title,
        message,
        type,
        data,
        createdAt: new Date(),
        isRead: false
      };

      user.notifications = user.notifications || [];
      user.notifications.unshift(notification);

      // Keep only last 50 notifications
      if (user.notifications.length > 50) {
        user.notifications = user.notifications.slice(0, 50);
      }

      await user.save();

      this.log('info', 'In-app notification sent', {
        userId: user._id,
        title,
        type
      });

      return { success: true, notificationId: notification._id };
    } catch (error) {
      this.handleError(error, 'Send in-app notification', { userId: user._id });
    }
  }

  /**
   * Send email notification
   */
  async sendEmailNotification(user, { title, message, type, data }) {
    try {
      const emailHtml = this.generateNotificationEmailHTML({
        user,
        title,
        message,
        type,
        data
      });

      const emailText = this.generateNotificationEmailText({
        user,
        title,
        message,
        type,
        data
      });

      const result = await this.emailService.sendEmail({
        to: user.email,
        subject: title,
        html: emailHtml,
        text: emailText
      });

      return { success: true, messageId: result.data.messageId };
    } catch (error) {
      this.log('error', 'Failed to send email notification', {
        userId: user._id,
        error: error.message
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Send push notification (placeholder implementation)
   */
  async sendPushNotification(user, { title, message, type, data }) {
    try {
      // Push notification implementation would go here
      // This could integrate with Firebase Cloud Messaging, OneSignal, etc.
      
      this.log('info', 'Push notification sent (placeholder)', {
        userId: user._id,
        title,
        type
      });

      return { success: true, provider: 'placeholder' };
    } catch (error) {
      this.log('error', 'Failed to send push notification', {
        userId: user._id,
        error: error.message
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Send course-related notifications
   */
  async sendCourseNotification(courseId, { title, message, type = 'info', excludeUsers = [] }) {
    try {
      // Get all users enrolled in the course
      const enrolledUsers = await User.find({
        'enrolledCourses.course': courseId,
        _id: { $nin: excludeUsers }
      }).select('_id email firstName lastName');

      if (enrolledUsers.length === 0) {
        return this.formatResponse([], 'No enrolled users found');
      }

      const recipients = enrolledUsers.map(user => user._id.toString());

      return await this.sendNotification({
        recipients,
        title,
        message,
        type,
        channels: ['in-app', 'email'],
        data: { courseId }
      });
    } catch (error) {
      this.handleError(error, 'Send course notification', { courseId });
    }
  }

  /**
   * Send batch-related notifications
   */
  async sendBatchNotification(batchId, { title, message, type = 'info', excludeUsers = [] }) {
    try {
      const Batch = require('../models/Batch');
      const batch = await Batch.findById(batchId).populate('students.student', '_id email firstName lastName');

      if (!batch) {
        throw new Error('Batch not found');
      }

      const recipients = batch.students
        .filter(student => !excludeUsers.includes(student.student._id.toString()))
        .map(student => student.student._id.toString());

      if (recipients.length === 0) {
        return this.formatResponse([], 'No batch students found');
      }

      return await this.sendNotification({
        recipients,
        title,
        message,
        type,
        channels: ['in-app', 'email'],
        data: { batchId }
      });
    } catch (error) {
      this.handleError(error, 'Send batch notification', { batchId });
    }
  }

  /**
   * Send system-wide notifications
   */
  async sendSystemNotification({ title, message, type = 'info', userRole = null }) {
    try {
      let query = { isActive: true };
      
      if (userRole) {
        query.role = userRole;
      }

      const users = await User.find(query).select('_id');
      const recipients = users.map(user => user._id.toString());

      if (recipients.length === 0) {
        return this.formatResponse([], 'No users found');
      }

      return await this.sendNotification({
        recipients,
        title,
        message,
        type,
        channels: ['in-app'],
        data: { systemNotification: true }
      });
    } catch (error) {
      this.handleError(error, 'Send system notification');
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(userId, notificationId) {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }

      const notification = user.notifications.id(notificationId);
      
      if (!notification) {
        throw new Error('Notification not found');
      }

      notification.isRead = true;
      notification.readAt = new Date();

      await user.save();

      return this.formatResponse({ notificationId }, 'Notification marked as read');
    } catch (error) {
      this.handleError(error, 'Mark notification as read', { userId, notificationId });
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(userId) {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }

      user.notifications.forEach(notification => {
        if (!notification.isRead) {
          notification.isRead = true;
          notification.readAt = new Date();
        }
      });

      await user.save();

      return this.formatResponse({ userId }, 'All notifications marked as read');
    } catch (error) {
      this.handleError(error, 'Mark all notifications as read', { userId });
    }
  }

  /**
   * Get user notifications
   */
  async getUserNotifications(userId, { page = 1, limit = 20, unreadOnly = false } = {}) {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        throw new Error('User not found');
      }

      let notifications = user.notifications || [];

      if (unreadOnly) {
        notifications = notifications.filter(notification => !notification.isRead);
      }

      const total = notifications.length;
      const skip = (page - 1) * limit;
      const paginatedNotifications = notifications.slice(skip, skip + limit);

      return this.formatResponse({
        notifications: paginatedNotifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        unreadCount: notifications.filter(n => !n.isRead).length
      }, 'Notifications retrieved successfully');
    } catch (error) {
      this.handleError(error, 'Get user notifications', { userId });
    }
  }

  /**
   * Generate notification email HTML
   */
  generateNotificationEmailHTML({ user, title, message, type, data }) {
    const typeColors = {
      info: '#2563eb',
      success: '#16a34a',
      warning: '#d97706',
      error: '#dc2626'
    };

    const color = typeColors[type] || typeColors.info;

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: ${color}; color: white; padding: 20px; border-radius: 8px 8px 0 0;">
          <h1 style="margin: 0; font-size: 24px;">${title}</h1>
        </div>
        <div style="background-color: #f9fafb; padding: 20px; border-radius: 0 0 8px 8px;">
          <p>Dear ${user.firstName},</p>
          <div style="background-color: white; padding: 20px; border-radius: 6px; margin: 20px 0;">
            ${message}
          </div>
          <p>Best regards,<br>The RJWU Team</p>
        </div>
      </div>
    `;
  }

  /**
   * Generate notification email text
   */
  generateNotificationEmailText({ user, title, message, type, data }) {
    return `
${title}

Dear ${user.firstName},

${message}

Best regards,
The RJWU Team
    `;
  }
}

module.exports = new NotificationService();
