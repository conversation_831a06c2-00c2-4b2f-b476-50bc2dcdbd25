import React, { useState } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import DashboardLayout from '@/components/layouts/DashboardLayout'
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  StarIcon,
  ClockIcon,
  UserGroupIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'

// Mock data - in real app, this would come from API
const mockCourses = [
  {
    id: 1,
    title: 'Complete React.js Development Course',
    description: 'Master React.js from basics to advanced concepts including hooks, context, and modern patterns.',
    instructor: {
      name: 'Dr. <PERSON>',
      avatar: 'https://via.placeholder.com/40x40',
      rating: 4.8
    },
    thumbnail: 'https://via.placeholder.com/400x250',
    price: 2999,
    originalPrice: 4999,
    rating: 4.7,
    studentsCount: 1250,
    duration: '40 hours',
    level: 'Intermediate',
    category: 'Web Development',
    tags: ['React', 'JavaScript', 'Frontend'],
    isEnrolled: false,
    isBestseller: true
  },
  {
    id: 2,
    title: 'Node.js Backend Development Masterclass',
    description: 'Build scalable backend applications with Node.js, Express, and MongoDB.',
    instructor: {
      name: 'Prof. <PERSON>',
      avatar: 'https://via.placeholder.com/40x40',
      rating: 4.9
    },
    thumbnail: 'https://via.placeholder.com/400x250',
    price: 3499,
    originalPrice: 5999,
    rating: 4.8,
    studentsCount: 890,
    duration: '35 hours',
    level: 'Advanced',
    category: 'Backend Development',
    tags: ['Node.js', 'Express', 'MongoDB'],
    isEnrolled: true,
    isBestseller: false
  },
  {
    id: 3,
    title: 'Full Stack Web Development Bootcamp',
    description: 'Complete full stack development course covering frontend, backend, and deployment.',
    instructor: {
      name: 'Dr. Emily Rodriguez',
      avatar: 'https://via.placeholder.com/40x40',
      rating: 4.6
    },
    thumbnail: 'https://via.placeholder.com/400x250',
    price: 4999,
    originalPrice: 7999,
    rating: 4.6,
    studentsCount: 2100,
    duration: '60 hours',
    level: 'Beginner',
    category: 'Full Stack',
    tags: ['HTML', 'CSS', 'JavaScript', 'React', 'Node.js'],
    isEnrolled: false,
    isBestseller: true
  },
  {
    id: 4,
    title: 'Python for Data Science',
    description: 'Learn Python programming for data analysis, visualization, and machine learning.',
    instructor: {
      name: 'Dr. James Kumar',
      avatar: 'https://via.placeholder.com/40x40',
      rating: 4.7
    },
    thumbnail: 'https://via.placeholder.com/400x250',
    price: 3999,
    originalPrice: 6999,
    rating: 4.5,
    studentsCount: 1680,
    duration: '45 hours',
    level: 'Intermediate',
    category: 'Data Science',
    tags: ['Python', 'Pandas', 'NumPy', 'Machine Learning'],
    isEnrolled: false,
    isBestseller: false
  }
]

const categories = ['All', 'Web Development', 'Backend Development', 'Full Stack', 'Data Science', 'Mobile Development']
const levels = ['All', 'Beginner', 'Intermediate', 'Advanced']
const sortOptions = ['Newest', 'Most Popular', 'Highest Rated', 'Price: Low to High', 'Price: High to Low']

export default function CoursesPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [selectedLevel, setSelectedLevel] = useState('All')
  const [sortBy, setSortBy] = useState('Most Popular')
  const [showFilters, setShowFilters] = useState(false)

  const filteredCourses = mockCourses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'All' || course.category === selectedCategory
    const matchesLevel = selectedLevel === 'All' || course.level === selectedLevel
    
    return matchesSearch && matchesCategory && matchesLevel
  })

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <StarIconSolid
            key={star}
            className={`h-4 w-4 ${
              star <= Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">{rating}</span>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Courses - RJWU Platform</title>
        <meta name="description" content="Browse and enroll in courses on RJWU educational platform" />
      </Head>

      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">All Courses</h1>
              <p className="mt-1 text-sm text-gray-600">
                Discover and enroll in courses to advance your skills
              </p>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search courses..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="lg:hidden flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                <FunnelIcon className="h-5 w-5 mr-2" />
                Filters
              </button>

              {/* Desktop Filters */}
              <div className="hidden lg:flex items-center space-x-4">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>

                <select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  {levels.map(level => (
                    <option key={level} value={level}>{level}</option>
                  ))}
                </select>

                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                >
                  {sortOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Mobile Filters */}
            {showFilters && (
              <div className="lg:hidden mt-4 pt-4 border-t border-gray-200 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Level</label>
                  <select
                    value={selectedLevel}
                    onChange={(e) => setSelectedLevel(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    {levels.map(level => (
                      <option key={level} value={level}>{level}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Sort by</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-blue-500 focus:border-blue-500"
                  >
                    {sortOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>
              </div>
            )}
          </div>

          {/* Results Count */}
          <div className="text-sm text-gray-600">
            Showing {filteredCourses.length} of {mockCourses.length} courses
          </div>

          {/* Courses Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCourses.map((course) => (
              <div key={course.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative">
                  <img
                    src={course.thumbnail}
                    alt={course.title}
                    className="w-full h-48 object-cover"
                  />
                  {course.isBestseller && (
                    <span className="absolute top-2 left-2 bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded">
                      Bestseller
                    </span>
                  )}
                  {course.isEnrolled && (
                    <span className="absolute top-2 right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded">
                      Enrolled
                    </span>
                  )}
                </div>

                <div className="p-6">
                  <div className="flex items-center mb-2">
                    <img
                      src={course.instructor.avatar}
                      alt={course.instructor.name}
                      className="w-8 h-8 rounded-full mr-2"
                    />
                    <span className="text-sm text-gray-600">{course.instructor.name}</span>
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                    {course.title}
                  </h3>

                  <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                    {course.description}
                  </p>

                  <div className="flex items-center justify-between mb-4">
                    {renderStars(course.rating)}
                    <span className="text-sm text-gray-500">({course.studentsCount} students)</span>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <div className="flex items-center">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      {course.duration}
                    </div>
                    <div className="flex items-center">
                      <AcademicCapIcon className="h-4 w-4 mr-1" />
                      {course.level}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-lg font-bold text-gray-900">₹{course.price}</span>
                      {course.originalPrice > course.price && (
                        <span className="ml-2 text-sm text-gray-500 line-through">₹{course.originalPrice}</span>
                      )}
                    </div>
                    <Link
                      href={`/courses/${course.id}`}
                      className={`px-4 py-2 rounded-md text-sm font-medium ${
                        course.isEnrolled
                          ? 'bg-green-100 text-green-800 hover:bg-green-200'
                          : 'bg-blue-600 text-white hover:bg-blue-700'
                      }`}
                    >
                      {course.isEnrolled ? 'Continue' : 'View Details'}
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* No Results */}
          {filteredCourses.length === 0 && (
            <div className="text-center py-12">
              <AcademicCapIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No courses found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or filters.
              </p>
            </div>
          )}
        </div>
      </DashboardLayout>
    </>
  )
}
