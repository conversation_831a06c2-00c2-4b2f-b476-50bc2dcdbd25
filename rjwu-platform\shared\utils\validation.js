/**
 * Shared Validation Utilities
 * Common validation functions used across frontend and backend
 */

// Email validation regex
const EMAIL_REGEX = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

// Indian phone number regex (10 digits, optionally with +91)
const PHONE_REGEX = /^(\+91|91)?[6-9]\d{9}$/;

// Password strength regex (at least 8 chars, 1 uppercase, 1 lowercase, 1 number)
const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;

// PIN code regex (6 digits)
const PINCODE_REGEX = /^[1-9][0-9]{5}$/;

// Course code regex (alphanumeric, 3-10 characters)
const COURSE_CODE_REGEX = /^[A-Z0-9]{3,10}$/;

/**
 * Validate email address
 * @param {string} email - Email to validate
 * @returns {boolean} - True if valid
 */
function isValidEmail(email) {
  if (!email || typeof email !== 'string') return false;
  return EMAIL_REGEX.test(email.trim().toLowerCase());
}

/**
 * Validate phone number (Indian format)
 * @param {string} phone - Phone number to validate
 * @returns {boolean} - True if valid
 */
function isValidPhone(phone) {
  if (!phone || typeof phone !== 'string') return false;
  const cleanPhone = phone.replace(/[\s-()]/g, '');
  return PHONE_REGEX.test(cleanPhone);
}

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {object} - Validation result with details
 */
function validatePassword(password) {
  const result = {
    isValid: false,
    errors: [],
    strength: 'weak'
  };

  if (!password || typeof password !== 'string') {
    result.errors.push('Password is required');
    return result;
  }

  if (password.length < 8) {
    result.errors.push('Password must be at least 8 characters long');
  }

  if (!/[a-z]/.test(password)) {
    result.errors.push('Password must contain at least one lowercase letter');
  }

  if (!/[A-Z]/.test(password)) {
    result.errors.push('Password must contain at least one uppercase letter');
  }

  if (!/\d/.test(password)) {
    result.errors.push('Password must contain at least one number');
  }

  if (password.length >= 12 && /[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    result.strength = 'strong';
  } else if (password.length >= 10) {
    result.strength = 'medium';
  }

  result.isValid = result.errors.length === 0;
  return result;
}

/**
 * Validate Indian PIN code
 * @param {string} pincode - PIN code to validate
 * @returns {boolean} - True if valid
 */
function isValidPincode(pincode) {
  if (!pincode || typeof pincode !== 'string') return false;
  return PINCODE_REGEX.test(pincode.trim());
}

/**
 * Validate name (first name, last name)
 * @param {string} name - Name to validate
 * @returns {boolean} - True if valid
 */
function isValidName(name) {
  if (!name || typeof name !== 'string') return false;
  const trimmedName = name.trim();
  return trimmedName.length >= 2 && trimmedName.length <= 50 && /^[a-zA-Z\s.'-]+$/.test(trimmedName);
}

/**
 * Validate course title
 * @param {string} title - Course title to validate
 * @returns {boolean} - True if valid
 */
function isValidCourseTitle(title) {
  if (!title || typeof title !== 'string') return false;
  const trimmedTitle = title.trim();
  return trimmedTitle.length >= 5 && trimmedTitle.length <= 200;
}

/**
 * Validate course price
 * @param {number} price - Price to validate
 * @returns {boolean} - True if valid
 */
function isValidPrice(price) {
  return typeof price === 'number' && price >= 0 && price <= 100000 && Number.isFinite(price);
}

/**
 * Validate course duration (in hours)
 * @param {number} duration - Duration to validate
 * @returns {boolean} - True if valid
 */
function isValidDuration(duration) {
  return typeof duration === 'number' && duration > 0 && duration <= 1000 && Number.isFinite(duration);
}

/**
 * Validate URL
 * @param {string} url - URL to validate
 * @returns {boolean} - True if valid
 */
function isValidUrl(url) {
  if (!url || typeof url !== 'string') return false;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Validate date string
 * @param {string} dateString - Date string to validate
 * @returns {boolean} - True if valid
 */
function isValidDate(dateString) {
  if (!dateString || typeof dateString !== 'string') return false;
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
}

/**
 * Validate age (must be between 13 and 100)
 * @param {number} age - Age to validate
 * @returns {boolean} - True if valid
 */
function isValidAge(age) {
  return typeof age === 'number' && age >= 13 && age <= 100 && Number.isInteger(age);
}

/**
 * Validate rating (1-5 scale)
 * @param {number} rating - Rating to validate
 * @returns {boolean} - True if valid
 */
function isValidRating(rating) {
  return typeof rating === 'number' && rating >= 1 && rating <= 5 && Number.isFinite(rating);
}

/**
 * Validate MongoDB ObjectId
 * @param {string} id - ID to validate
 * @returns {boolean} - True if valid
 */
function isValidObjectId(id) {
  if (!id || typeof id !== 'string') return false;
  return /^[0-9a-fA-F]{24}$/.test(id);
}

/**
 * Sanitize string input
 * @param {string} input - Input to sanitize
 * @param {number} maxLength - Maximum length (default: 1000)
 * @returns {string} - Sanitized string
 */
function sanitizeString(input, maxLength = 1000) {
  if (!input || typeof input !== 'string') return '';
  
  return input
    .trim()
    .slice(0, maxLength)
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/\s+/g, ' '); // Normalize whitespace
}

/**
 * Validate user registration data
 * @param {object} userData - User data to validate
 * @returns {object} - Validation result
 */
function validateUserRegistration(userData) {
  const errors = {};

  if (!isValidName(userData.firstName)) {
    errors.firstName = 'First name must be 2-50 characters and contain only letters, spaces, dots, hyphens, and apostrophes';
  }

  if (!isValidName(userData.lastName)) {
    errors.lastName = 'Last name must be 2-50 characters and contain only letters, spaces, dots, hyphens, and apostrophes';
  }

  if (!isValidEmail(userData.email)) {
    errors.email = 'Please enter a valid email address';
  }

  if (!isValidPhone(userData.phone)) {
    errors.phone = 'Please enter a valid Indian phone number';
  }

  const passwordValidation = validatePassword(userData.password);
  if (!passwordValidation.isValid) {
    errors.password = passwordValidation.errors;
  }

  if (userData.role && !['student', 'instructor', 'admin'].includes(userData.role)) {
    errors.role = 'Invalid user role';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Validate course creation data
 * @param {object} courseData - Course data to validate
 * @returns {object} - Validation result
 */
function validateCourseCreation(courseData) {
  const errors = {};

  if (!isValidCourseTitle(courseData.title)) {
    errors.title = 'Course title must be 5-200 characters long';
  }

  if (!courseData.description || courseData.description.trim().length < 20) {
    errors.description = 'Course description must be at least 20 characters long';
  }

  if (!isValidPrice(courseData.price)) {
    errors.price = 'Price must be a valid number between 0 and 100,000';
  }

  if (!isValidDuration(courseData.duration)) {
    errors.duration = 'Duration must be a positive number up to 1000 hours';
  }

  if (!courseData.category || courseData.category.trim().length < 2) {
    errors.category = 'Category is required';
  }

  if (!['beginner', 'intermediate', 'advanced'].includes(courseData.level)) {
    errors.level = 'Level must be beginner, intermediate, or advanced';
  }

  if (!isValidObjectId(courseData.instructor)) {
    errors.instructor = 'Valid instructor ID is required';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Validate payment data
 * @param {object} paymentData - Payment data to validate
 * @returns {object} - Validation result
 */
function validatePaymentData(paymentData) {
  const errors = {};

  if (!isValidObjectId(paymentData.user)) {
    errors.user = 'Valid user ID is required';
  }

  if (!isValidObjectId(paymentData.course)) {
    errors.course = 'Valid course ID is required';
  }

  if (!isValidPrice(paymentData.amount)) {
    errors.amount = 'Valid amount is required';
  }

  if (!['INR', 'USD'].includes(paymentData.currency)) {
    errors.currency = 'Currency must be INR or USD';
  }

  if (!['razorpay', 'payu', 'stripe'].includes(paymentData.gateway)) {
    errors.gateway = 'Invalid payment gateway';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Common validation patterns
 */
const VALIDATION_PATTERNS = {
  EMAIL: EMAIL_REGEX,
  PHONE: PHONE_REGEX,
  PASSWORD: PASSWORD_REGEX,
  PINCODE: PINCODE_REGEX,
  COURSE_CODE: COURSE_CODE_REGEX
};

/**
 * Common validation messages
 */
const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PHONE: 'Please enter a valid phone number',
  WEAK_PASSWORD: 'Password must be at least 8 characters with uppercase, lowercase, and number',
  INVALID_URL: 'Please enter a valid URL',
  INVALID_DATE: 'Please enter a valid date',
  INVALID_PRICE: 'Please enter a valid price',
  INVALID_RATING: 'Rating must be between 1 and 5'
};

// Export for both CommonJS (Node.js) and ES modules (frontend)
const validationUtils = {
  isValidEmail,
  isValidPhone,
  validatePassword,
  isValidPincode,
  isValidName,
  isValidCourseTitle,
  isValidPrice,
  isValidDuration,
  isValidUrl,
  isValidDate,
  isValidAge,
  isValidRating,
  isValidObjectId,
  sanitizeString,
  validateUserRegistration,
  validateCourseCreation,
  validatePaymentData,
  VALIDATION_PATTERNS,
  VALIDATION_MESSAGES
};

// CommonJS export (Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = validationUtils;
}

// ES module export (frontend)
if (typeof window !== 'undefined') {
  window.ValidationUtils = validationUtils;
}
