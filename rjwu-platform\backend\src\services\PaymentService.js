const Razorpay = require('razorpay');
const crypto = require('crypto');
const BaseService = require('./BaseService');
const config = require('../config/config');
const Payment = require('../models/Payment');
const User = require('../models/User');
const Course = require('../models/Course');

/**
 * Payment Service
 * Handles all payment-related operations
 */
class PaymentService extends BaseService {
  constructor() {
    super('PaymentService');
    this.initializePaymentGateways();
  }

  /**
   * Initialize payment gateways
   */
  initializePaymentGateways() {
    try {
      // Initialize Razorpay
      this.razorpay = new Razorpay({
        key_id: config.RAZORPAY_KEY_ID,
        key_secret: config.RAZORPAY_KEY_SECRET
      });

      this.log('info', 'Payment gateways initialized successfully');
    } catch (error) {
      this.handleError(error, 'Payment gateway initialization');
    }
  }

  /**
   * Create payment order
   * @param {Object} orderData - Order data
   * @param {string} orderData.userId - User ID
   * @param {string} orderData.courseId - Course ID
   * @param {number} orderData.amount - Amount in rupees
   * @param {string} orderData.currency - Currency (default: INR)
   * @param {string} orderData.gateway - Payment gateway (razorpay/payu)
   */
  async createPaymentOrder({ userId, courseId, amount, currency = 'INR', gateway = 'razorpay' }) {
    try {
      this.validateRequired({ userId, courseId, amount }, ['userId', 'courseId', 'amount']);

      // Verify user and course exist
      const user = await User.findById(userId);
      const course = await Course.findById(courseId);

      if (!user) {
        throw new Error('User not found');
      }

      if (!course) {
        throw new Error('Course not found');
      }

      // Check if user is already enrolled
      const isEnrolled = user.enrolledCourses.some(
        enrollment => enrollment.course.toString() === courseId
      );

      if (isEnrolled) {
        throw new Error('User is already enrolled in this course');
      }

      let paymentOrder;
      const amountInPaise = Math.round(amount * 100); // Convert to paise for Razorpay

      if (gateway === 'razorpay') {
        paymentOrder = await this.createRazorpayOrder({
          amount: amountInPaise,
          currency,
          receipt: `course_${courseId}_user_${userId}_${Date.now()}`
        });
      } else if (gateway === 'payu') {
        paymentOrder = await this.createPayUOrder({
          amount,
          currency,
          userId,
          courseId
        });
      } else {
        throw new Error('Unsupported payment gateway');
      }

      // Create payment record in database
      const payment = new Payment({
        user: userId,
        course: courseId,
        amount,
        currency,
        gateway,
        gatewayOrderId: paymentOrder.id,
        status: 'pending',
        metadata: {
          receipt: paymentOrder.receipt || `course_${courseId}_user_${userId}`,
          gatewayResponse: paymentOrder
        }
      });

      await payment.save();

      this.log('info', 'Payment order created successfully', {
        paymentId: payment._id,
        orderId: paymentOrder.id,
        amount,
        gateway
      });

      return this.formatResponse({
        payment: payment._id,
        orderId: paymentOrder.id,
        amount,
        currency,
        gateway,
        user: {
          id: user._id,
          name: user.fullName,
          email: user.email
        },
        course: {
          id: course._id,
          title: course.title,
          price: course.price
        }
      }, 'Payment order created successfully');

    } catch (error) {
      this.handleError(error, 'Create payment order', { userId, courseId, amount });
    }
  }

  /**
   * Create Razorpay order
   */
  async createRazorpayOrder({ amount, currency, receipt }) {
    try {
      const order = await this.razorpay.orders.create({
        amount,
        currency,
        receipt,
        payment_capture: 1
      });

      return order;
    } catch (error) {
      this.handleError(error, 'Create Razorpay order');
    }
  }

  /**
   * Create PayU order (placeholder implementation)
   */
  async createPayUOrder({ amount, currency, userId, courseId }) {
    try {
      // PayU implementation would go here
      // This is a placeholder for the actual PayU integration
      const orderId = `payu_${Date.now()}_${userId}_${courseId}`;
      
      return {
        id: orderId,
        amount,
        currency,
        status: 'created'
      };
    } catch (error) {
      this.handleError(error, 'Create PayU order');
    }
  }

  /**
   * Verify payment
   * @param {Object} paymentData - Payment verification data
   * @param {string} paymentData.paymentId - Payment ID from gateway
   * @param {string} paymentData.orderId - Order ID
   * @param {string} paymentData.signature - Payment signature
   * @param {string} paymentData.gateway - Payment gateway
   */
  async verifyPayment({ paymentId, orderId, signature, gateway = 'razorpay' }) {
    try {
      this.validateRequired({ paymentId, orderId }, ['paymentId', 'orderId']);

      let isValid = false;

      if (gateway === 'razorpay') {
        isValid = this.verifyRazorpaySignature({ paymentId, orderId, signature });
      } else if (gateway === 'payu') {
        isValid = await this.verifyPayUPayment({ paymentId, orderId });
      }

      if (!isValid) {
        throw new Error('Payment verification failed');
      }

      // Update payment status in database
      const payment = await Payment.findOne({ gatewayOrderId: orderId });
      
      if (!payment) {
        throw new Error('Payment record not found');
      }

      payment.status = 'completed';
      payment.gatewayPaymentId = paymentId;
      payment.verifiedAt = new Date();
      payment.metadata.verificationData = { paymentId, signature };

      await payment.save();

      // Enroll user in course
      await this.enrollUserInCourse(payment.user, payment.course);

      this.log('info', 'Payment verified and processed successfully', {
        paymentId: payment._id,
        gatewayPaymentId: paymentId,
        orderId
      });

      return this.formatResponse({
        payment: payment._id,
        status: 'completed',
        enrollmentStatus: 'success'
      }, 'Payment verified successfully');

    } catch (error) {
      // Update payment status to failed
      try {
        await Payment.findOneAndUpdate(
          { gatewayOrderId: orderId },
          { 
            status: 'failed',
            metadata: { 
              error: error.message,
              failedAt: new Date()
            }
          }
        );
      } catch (updateError) {
        this.log('error', 'Failed to update payment status', { updateError: updateError.message });
      }

      this.handleError(error, 'Verify payment', { paymentId, orderId });
    }
  }

  /**
   * Verify Razorpay signature
   */
  verifyRazorpaySignature({ paymentId, orderId, signature }) {
    try {
      const body = orderId + '|' + paymentId;
      const expectedSignature = crypto
        .createHmac('sha256', config.RAZORPAY_KEY_SECRET)
        .update(body.toString())
        .digest('hex');

      return expectedSignature === signature;
    } catch (error) {
      this.log('error', 'Razorpay signature verification failed', { error: error.message });
      return false;
    }
  }

  /**
   * Verify PayU payment (placeholder)
   */
  async verifyPayUPayment({ paymentId, orderId }) {
    try {
      // PayU verification logic would go here
      // This is a placeholder
      return true;
    } catch (error) {
      this.log('error', 'PayU payment verification failed', { error: error.message });
      return false;
    }
  }

  /**
   * Enroll user in course after successful payment
   */
  async enrollUserInCourse(userId, courseId) {
    try {
      const user = await User.findById(userId);
      const course = await Course.findById(courseId);

      if (!user || !course) {
        throw new Error('User or course not found');
      }

      // Add course to user's enrolled courses
      user.enrolledCourses.push({
        course: courseId,
        enrollmentDate: new Date(),
        progress: 0,
        status: 'active'
      });

      // Update course enrollment count
      course.enrollmentCount += 1;

      await Promise.all([user.save(), course.save()]);

      this.log('info', 'User enrolled in course successfully', {
        userId,
        courseId,
        courseTitle: course.title
      });

      return true;
    } catch (error) {
      this.handleError(error, 'Enroll user in course', { userId, courseId });
    }
  }

  /**
   * Get payment status
   * @param {string} paymentId - Payment ID
   */
  async getPaymentStatus(paymentId) {
    try {
      const payment = await Payment.findById(paymentId)
        .populate('user', 'firstName lastName email')
        .populate('course', 'title price');

      if (!payment) {
        throw new Error('Payment not found');
      }

      return this.formatResponse(payment, 'Payment status retrieved successfully');
    } catch (error) {
      this.handleError(error, 'Get payment status', { paymentId });
    }
  }

  /**
   * Process refund
   * @param {string} paymentId - Payment ID
   * @param {number} amount - Refund amount (optional, defaults to full amount)
   * @param {string} reason - Refund reason
   */
  async processRefund(paymentId, amount = null, reason = 'User requested refund') {
    try {
      const payment = await Payment.findById(paymentId);

      if (!payment) {
        throw new Error('Payment not found');
      }

      if (payment.status !== 'completed') {
        throw new Error('Cannot refund incomplete payment');
      }

      const refundAmount = amount || payment.amount;

      let refundResult;
      if (payment.gateway === 'razorpay') {
        refundResult = await this.processRazorpayRefund(payment.gatewayPaymentId, refundAmount * 100);
      } else if (payment.gateway === 'payu') {
        refundResult = await this.processPayURefund(payment.gatewayPaymentId, refundAmount);
      }

      // Update payment record
      payment.status = 'refunded';
      payment.refundAmount = refundAmount;
      payment.refundReason = reason;
      payment.refundedAt = new Date();
      payment.metadata.refundData = refundResult;

      await payment.save();

      // Remove course enrollment if full refund
      if (refundAmount === payment.amount) {
        await this.removeUserEnrollment(payment.user, payment.course);
      }

      this.log('info', 'Refund processed successfully', {
        paymentId,
        refundAmount,
        reason
      });

      return this.formatResponse({
        refundId: refundResult.id,
        amount: refundAmount,
        status: 'processed'
      }, 'Refund processed successfully');

    } catch (error) {
      this.handleError(error, 'Process refund', { paymentId, amount, reason });
    }
  }

  /**
   * Process Razorpay refund
   */
  async processRazorpayRefund(paymentId, amount) {
    try {
      const refund = await this.razorpay.payments.refund(paymentId, {
        amount,
        speed: 'normal'
      });

      return refund;
    } catch (error) {
      this.handleError(error, 'Process Razorpay refund');
    }
  }

  /**
   * Process PayU refund (placeholder)
   */
  async processPayURefund(paymentId, amount) {
    try {
      // PayU refund logic would go here
      return {
        id: `refund_${Date.now()}`,
        amount,
        status: 'processed'
      };
    } catch (error) {
      this.handleError(error, 'Process PayU refund');
    }
  }

  /**
   * Remove user enrollment after refund
   */
  async removeUserEnrollment(userId, courseId) {
    try {
      const user = await User.findById(userId);
      const course = await Course.findById(courseId);

      if (user && course) {
        // Remove course from user's enrolled courses
        user.enrolledCourses = user.enrolledCourses.filter(
          enrollment => enrollment.course.toString() !== courseId.toString()
        );

        // Decrease course enrollment count
        course.enrollmentCount = Math.max(0, course.enrollmentCount - 1);

        await Promise.all([user.save(), course.save()]);

        this.log('info', 'User enrollment removed after refund', {
          userId,
          courseId
        });
      }
    } catch (error) {
      this.handleError(error, 'Remove user enrollment', { userId, courseId });
    }
  }
}

module.exports = new PaymentService();
