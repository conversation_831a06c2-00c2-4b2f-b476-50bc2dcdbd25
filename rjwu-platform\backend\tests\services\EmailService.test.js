const EmailService = require('../../src/services/EmailService');
const User = require('../../src/models/User');

// Mock nodemailer
jest.mock('nodemailer', () => ({
  createTransporter: jest.fn(() => ({
    verify: jest.fn(() => Promise.resolve()),
    sendMail: jest.fn(() => Promise.resolve({
      messageId: 'test-message-id',
      response: '250 OK'
    }))
  }))
}));

describe('EmailService', () => {
  let user;

  beforeEach(async () => {
    const userData = global.testUtils.createTestUser();
    user = new User(userData);
    await user.save();

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('sendEmail', () => {
    test('should send email successfully', async () => {
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        html: '<h1>Test</h1>',
        text: 'Test'
      };

      const result = await EmailService.sendEmail(emailData);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Email sent successfully');
      expect(result.data.messageId).toBe('test-message-id');
    });

    test('should validate required fields', async () => {
      await expect(EmailService.sendEmail({})).rejects.toThrow('Missing required parameters');
    });

    test('should require either HTML or text content', async () => {
      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email'
        // Missing html and text
      };

      await expect(EmailService.sendEmail(emailData)).rejects.toThrow('Either HTML or text content is required');
    });
  });

  describe('sendWelcomeEmail', () => {
    test('should send welcome email', async () => {
      const result = await EmailService.sendWelcomeEmail(user);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Email sent successfully');
    });

    test('should generate welcome email content', () => {
      const html = EmailService.generateWelcomeEmailHTML(user);
      const text = EmailService.generateWelcomeEmailText(user);

      expect(html).toContain(user.firstName);
      expect(html).toContain('Welcome to RJWU Platform');
      expect(text).toContain(user.firstName);
      expect(text).toContain('Welcome to RJWU Platform');
    });
  });

  describe('sendEmailVerification', () => {
    test('should send email verification', async () => {
      const verificationToken = 'test-verification-token';
      const result = await EmailService.sendEmailVerification(user, verificationToken);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Email sent successfully');
    });

    test('should generate verification email content', () => {
      const verificationUrl = 'https://example.com/verify?token=test-token';
      const html = EmailService.generateEmailVerificationHTML(user, verificationUrl);
      const text = EmailService.generateEmailVerificationText(user, verificationUrl);

      expect(html).toContain(user.firstName);
      expect(html).toContain('Verify Your Email');
      expect(html).toContain(verificationUrl);
      expect(text).toContain(user.firstName);
      expect(text).toContain(verificationUrl);
    });
  });

  describe('sendPasswordReset', () => {
    test('should send password reset email', async () => {
      const resetToken = 'test-reset-token';
      const result = await EmailService.sendPasswordReset(user, resetToken);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Email sent successfully');
    });

    test('should generate password reset email content', () => {
      const resetUrl = 'https://example.com/reset?token=test-token';
      const html = EmailService.generatePasswordResetHTML(user, resetUrl);
      const text = EmailService.generatePasswordResetText(user, resetUrl);

      expect(html).toContain(user.firstName);
      expect(html).toContain('Password Reset');
      expect(html).toContain(resetUrl);
      expect(text).toContain(user.firstName);
      expect(text).toContain(resetUrl);
    });
  });

  describe('sendCourseEnrollmentConfirmation', () => {
    test('should send enrollment confirmation email', async () => {
      const course = {
        _id: 'course-id',
        title: 'Test Course',
        description: 'Test course description'
      };

      const result = await EmailService.sendCourseEnrollmentConfirmation(user, course);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Email sent successfully');
    });

    test('should generate enrollment confirmation email content', () => {
      const course = {
        _id: 'course-id',
        title: 'Test Course',
        description: 'Test course description'
      };

      const html = EmailService.generateEnrollmentConfirmationHTML(user, course);
      const text = EmailService.generateEnrollmentConfirmationText(user, course);

      expect(html).toContain(user.firstName);
      expect(html).toContain(course.title);
      expect(html).toContain('Course Enrollment Confirmed');
      expect(text).toContain(user.firstName);
      expect(text).toContain(course.title);
    });
  });

  describe('Email Template Generation', () => {
    test('should generate HTML templates with proper structure', () => {
      const html = EmailService.generateWelcomeEmailHTML(user);

      expect(html).toContain('<div');
      expect(html).toContain('font-family');
      expect(html).toContain('max-width');
      expect(html).toContain(user.firstName);
    });

    test('should generate text templates without HTML tags', () => {
      const text = EmailService.generateWelcomeEmailText(user);

      expect(text).not.toContain('<');
      expect(text).not.toContain('>');
      expect(text).toContain(user.firstName);
      expect(text).toContain('\n');
    });

    test('should include proper links in templates', () => {
      const verificationUrl = 'https://example.com/verify?token=test';
      const html = EmailService.generateEmailVerificationHTML(user, verificationUrl);

      expect(html).toContain(`href="${verificationUrl}"`);
      expect(html).toContain(verificationUrl);
    });
  });

  describe('Error Handling', () => {
    test('should handle email sending errors', async () => {
      // Mock email sending failure
      const nodemailer = require('nodemailer');
      nodemailer.createTransporter.mockReturnValue({
        verify: jest.fn(() => Promise.resolve()),
        sendMail: jest.fn(() => Promise.reject(new Error('SMTP Error')))
      });

      const emailData = {
        to: '<EMAIL>',
        subject: 'Test Email',
        html: '<h1>Test</h1>'
      };

      await expect(EmailService.sendEmail(emailData)).rejects.toThrow('SMTP Error');
    });

    test('should handle missing user data', async () => {
      const invalidUser = null;

      await expect(EmailService.sendWelcomeEmail(invalidUser)).rejects.toThrow();
    });
  });

  describe('Email Validation', () => {
    test('should validate email addresses', async () => {
      const emailData = {
        to: 'invalid-email',
        subject: 'Test',
        html: '<h1>Test</h1>'
      };

      // This would depend on your email validation implementation
      // For now, we'll assume the email service validates email format
      await expect(EmailService.sendEmail(emailData)).rejects.toThrow();
    });
  });
});
