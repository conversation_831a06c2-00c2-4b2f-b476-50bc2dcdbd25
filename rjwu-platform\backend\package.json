{"name": "rjwu-backend", "version": "1.0.0", "description": "RJWU EduTech Platform Backend API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:models": "jest tests/models", "test:controllers": "jest tests/controllers", "test:services": "jest tests/services", "build": "echo 'No build step required for Node.js'", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/"}, "keywords": ["edutech", "api", "nodejs", "express", "mongodb"], "author": "RJWU Team", "license": "UNLICENSED", "dependencies": {"aws-sdk": "^2.1692.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "compression": "^1.7.4", "connect-redis": "^7.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "razorpay": "^2.9.2", "redis": "^4.6.11", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "winston": "^3.11.0", "xss-clean": "^0.1.4"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}