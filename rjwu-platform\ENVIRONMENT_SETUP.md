# Environment Configuration Guide

## Overview

This document explains the environment configuration for the RJWU EduTech Platform. The platform uses environment variables to configure different aspects of the application for development, testing, and production environments.

## Environment Files

### Backend Environment (`.env`)
Located at: `backend/.env`

**Key Configuration Areas:**
- **Server**: Port and environment settings
- **Database**: MongoDB and Redis connection strings
- **Authentication**: JWT secrets and expiration
- **Email**: SMTP configuration for notifications
- **File Storage**: Cloudinary configuration for media uploads
- **Payments**: Razorpay and PayU integration keys
- **Live Streaming**: Agora.io configuration for video calls
- **Security**: Rate limiting and session management

### Frontend Environment (`.env.local`)
Located at: `frontend/.env.local`

**Key Configuration Areas:**
- **App**: Basic application settings and URLs
- **API**: Backend API endpoint configuration
- **Authentication**: NextAuth.js configuration
- **Third-party Services**: Stripe, analytics, error tracking
- **Feature Flags**: Enable/disable features for development

## Development Setup

### 1. Backend Configuration

```bash
cd backend
cp .env.example .env
# Edit .env with your development values
```

**Important Development Settings:**
- `NODE_ENV=development` - Enables debug logging and development features
- `MONGODB_URI=mongodb://localhost:27017/rjwu_dev` - Local MongoDB instance
- `JWT_SECRET` - Use a strong secret for development (provided)
- `EMAIL_*` - Configured for Ethereal Email (development email testing)

### 2. Frontend Configuration

```bash
cd frontend
cp .env.example .env.local
# Edit .env.local with your development values
```

**Important Development Settings:**
- `NEXT_PUBLIC_API_URL=http://localhost:5000/api` - Points to local backend
- `NEXT_PUBLIC_DEBUG=true` - Enables debug features
- `NEXT_PUBLIC_ENABLE_OFFLINE=false` - Disabled for development

## Production Considerations

### Security
- Change all default secrets and keys
- Use strong, randomly generated JWT secrets
- Configure proper CORS origins
- Enable HTTPS in production

### Services
- Set up actual Cloudinary account for file storage
- Configure real payment gateway credentials
- Set up production email service (SendGrid, AWS SES, etc.)
- Configure production database with proper authentication

### Monitoring
- Enable Sentry for error tracking
- Configure Google Analytics
- Set up proper logging levels

## Required External Services

### Development
- MongoDB (local or MongoDB Atlas)
- Redis (local or Redis Cloud)
- Ethereal Email (for email testing)

### Production
- MongoDB Atlas or self-hosted MongoDB
- Redis Cloud or self-hosted Redis
- Cloudinary (file storage)
- Razorpay/PayU (payments)
- Email service provider
- Agora.io (live streaming)

## Environment Variables Reference

### Critical Variables (Must Change for Production)
- `JWT_SECRET` - JWT signing secret
- `SESSION_SECRET` - Session encryption secret
- `MONGODB_URI` - Database connection string
- `CLOUDINARY_*` - File storage credentials
- `RAZORPAY_*` / `PAYU_*` - Payment gateway credentials

### Optional Variables (Can use defaults)
- `PORT` - Server port (default: 5000)
- `RATE_LIMIT_*` - Rate limiting configuration
- `MAX_FILE_SIZE` - File upload limits
- Feature flags for enabling/disabling features

## Troubleshooting

### Common Issues
1. **Database Connection**: Ensure MongoDB is running and accessible
2. **Redis Connection**: Verify Redis server is running
3. **CORS Errors**: Check FRONTEND_URL matches your frontend URL
4. **File Uploads**: Verify Cloudinary credentials and upload path

### Debug Mode
Set `DEBUG=rjwu:*` in backend and `NEXT_PUBLIC_DEBUG=true` in frontend for verbose logging.
