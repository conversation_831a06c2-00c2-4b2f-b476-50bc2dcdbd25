declare const _default: {
    bf: {
        ADD: typeof import("./bloom/ADD");
        add: typeof import("./bloom/ADD");
        CARD: typeof import("./bloom/CARD");
        card: typeof import("./bloom/CARD");
        EXISTS: typeof import("./bloom/EXISTS");
        exists: typeof import("./bloom/EXISTS");
        INFO: typeof import("./bloom/INFO");
        info: typeof import("./bloom/INFO");
        INSERT: typeof import("./bloom/INSERT");
        insert: typeof import("./bloom/INSERT");
        LOADCHUNK: typeof import("./bloom/LOADCHUNK");
        loadChunk: typeof import("./bloom/LOADCHUNK");
        MADD: typeof import("./bloom/MADD");
        mAdd: typeof import("./bloom/MADD");
        MEXISTS: typeof import("./bloom/MEXISTS");
        mExists: typeof import("./bloom/MEXISTS");
        RESERVE: typeof import("./bloom/RESERVE");
        reserve: typeof import("./bloom/RESERVE");
        SCANDUMP: typeof import("./bloom/SCANDUMP");
        scanDump: typeof import("./bloom/SCANDUMP");
    };
    cms: {
        INCRBY: typeof import("./count-min-sketch/INCRBY");
        incrBy: typeof import("./count-min-sketch/INCRBY");
        INFO: typeof import("./count-min-sketch/INFO");
        info: typeof import("./count-min-sketch/INFO");
        INITBYDIM: typeof import("./count-min-sketch/INITBYDIM");
        initByDim: typeof import("./count-min-sketch/INITBYDIM");
        INITBYPROB: typeof import("./count-min-sketch/INITBYPROB");
        initByProb: typeof import("./count-min-sketch/INITBYPROB");
        MERGE: typeof import("./count-min-sketch/MERGE");
        merge: typeof import("./count-min-sketch/MERGE");
        QUERY: typeof import("./count-min-sketch/QUERY");
        query: typeof import("./count-min-sketch/QUERY");
    };
    cf: {
        ADD: typeof import("./cuckoo/ADD");
        add: typeof import("./cuckoo/ADD");
        ADDNX: typeof import("./cuckoo/ADDNX");
        addNX: typeof import("./cuckoo/ADDNX");
        COUNT: typeof import("./cuckoo/COUNT");
        count: typeof import("./cuckoo/COUNT");
        DEL: typeof import("./cuckoo/DEL");
        del: typeof import("./cuckoo/DEL");
        EXISTS: typeof import("./cuckoo/EXISTS");
        exists: typeof import("./cuckoo/EXISTS");
        INFO: typeof import("./cuckoo/INFO");
        info: typeof import("./cuckoo/INFO");
        INSERT: typeof import("./cuckoo/INSERT");
        insert: typeof import("./cuckoo/INSERT");
        INSERTNX: typeof import("./cuckoo/INSERTNX");
        insertNX: typeof import("./cuckoo/INSERTNX");
        LOADCHUNK: typeof import("./cuckoo/LOADCHUNK");
        loadChunk: typeof import("./cuckoo/LOADCHUNK");
        RESERVE: typeof import("./cuckoo/RESERVE");
        reserve: typeof import("./cuckoo/RESERVE");
        SCANDUMP: typeof import("./cuckoo/SCANDUMP");
        scanDump: typeof import("./cuckoo/SCANDUMP");
    };
    tDigest: {
        ADD: typeof import("./t-digest/ADD");
        add: typeof import("./t-digest/ADD");
        BYRANK: typeof import("./t-digest/BYRANK");
        byRank: typeof import("./t-digest/BYRANK");
        BYREVRANK: typeof import("./t-digest/BYREVRANK");
        byRevRank: typeof import("./t-digest/BYREVRANK");
        CDF: typeof import("./t-digest/CDF");
        cdf: typeof import("./t-digest/CDF");
        CREATE: typeof import("./t-digest/CREATE");
        create: typeof import("./t-digest/CREATE");
        INFO: typeof import("./t-digest/INFO");
        info: typeof import("./t-digest/INFO");
        MAX: typeof import("./t-digest/MAX");
        max: typeof import("./t-digest/MAX");
        MERGE: typeof import("./t-digest/MERGE");
        merge: typeof import("./t-digest/MERGE");
        MIN: typeof import("./t-digest/MIN");
        min: typeof import("./t-digest/MIN");
        QUANTILE: typeof import("./t-digest/QUANTILE");
        quantile: typeof import("./t-digest/QUANTILE");
        RANK: typeof import("./t-digest/RANK");
        rank: typeof import("./t-digest/RANK");
        RESET: typeof import("./t-digest/RESET");
        reset: typeof import("./t-digest/RESET");
        REVRANK: typeof import("./t-digest/REVRANK");
        revRank: typeof import("./t-digest/REVRANK");
        TRIMMED_MEAN: typeof import("./t-digest/TRIMMED_MEAN");
        trimmedMean: typeof import("./t-digest/TRIMMED_MEAN");
    };
    topK: {
        ADD: typeof import("./top-k/ADD");
        add: typeof import("./top-k/ADD");
        COUNT: typeof import("./top-k/COUNT");
        count: typeof import("./top-k/COUNT");
        INCRBY: typeof import("./top-k/INCRBY");
        incrBy: typeof import("./top-k/INCRBY");
        INFO: typeof import("./top-k/INFO");
        info: typeof import("./top-k/INFO");
        LIST_WITHCOUNT: typeof import("./top-k/LIST_WITHCOUNT");
        listWithCount: typeof import("./top-k/LIST_WITHCOUNT");
        LIST: typeof import("./top-k/LIST");
        list: typeof import("./top-k/LIST");
        QUERY: typeof import("./top-k/QUERY");
        query: typeof import("./top-k/QUERY");
        RESERVE: typeof import("./top-k/RESERVE");
        reserve: typeof import("./top-k/RESERVE");
    };
};
export default _default;
