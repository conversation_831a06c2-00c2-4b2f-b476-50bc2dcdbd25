/**
 * Shared Constants and Enums
 * Common constants used across frontend and backend
 */

/**
 * User Roles
 */
const USER_ROLES = {
  STUDENT: 'student',
  INSTRUCTOR: 'instructor',
  ADMIN: 'admin'
};

/**
 * User Status
 */
const USER_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended',
  PENDING: 'pending'
};

/**
 * Course Levels
 */
const COURSE_LEVELS = {
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced'
};

/**
 * Course Status
 */
const COURSE_STATUS = {
  DRAFT: 'draft',
  PUBLISHED: 'published',
  ARCHIVED: 'archived',
  SUSPENDED: 'suspended'
};

/**
 * Course Categories
 */
const COURSE_CATEGORIES = {
  WEB_DEVELOPMENT: 'Web Development',
  MOBILE_DEVELOPMENT: 'Mobile Development',
  BACKEND_DEVELOPMENT: 'Backend Development',
  FRONTEND_DEVELOPMENT: 'Frontend Development',
  FULL_STACK: 'Full Stack Development',
  DATA_SCIENCE: 'Data Science',
  MACHINE_LEARNING: 'Machine Learning',
  ARTIFICIAL_INTELLIGENCE: 'Artificial Intelligence',
  DEVOPS: 'DevOps',
  CLOUD_COMPUTING: 'Cloud Computing',
  CYBERSECURITY: 'Cybersecurity',
  BLOCKCHAIN: 'Blockchain',
  GAME_DEVELOPMENT: 'Game Development',
  UI_UX_DESIGN: 'UI/UX Design',
  DIGITAL_MARKETING: 'Digital Marketing',
  PROJECT_MANAGEMENT: 'Project Management',
  BUSINESS_ANALYSIS: 'Business Analysis',
  QUALITY_ASSURANCE: 'Quality Assurance',
  DATABASE: 'Database',
  NETWORKING: 'Networking'
};

/**
 * Payment Status
 */
const PAYMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
  PARTIALLY_REFUNDED: 'partially_refunded'
};

/**
 * Payment Gateways
 */
const PAYMENT_GATEWAYS = {
  RAZORPAY: 'razorpay',
  PAYU: 'payu',
  STRIPE: 'stripe',
  PAYPAL: 'paypal'
};

/**
 * Currencies
 */
const CURRENCIES = {
  INR: 'INR',
  USD: 'USD',
  EUR: 'EUR',
  GBP: 'GBP'
};

/**
 * Batch Status
 */
const BATCH_STATUS = {
  UPCOMING: 'upcoming',
  ACTIVE: 'active',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  SUSPENDED: 'suspended'
};

/**
 * Session Types
 */
const SESSION_TYPES = {
  LIVE: 'live',
  RECORDED: 'recorded',
  WORKSHOP: 'workshop',
  QA: 'qa',
  ASSESSMENT: 'assessment'
};

/**
 * Session Status
 */
const SESSION_STATUS = {
  SCHEDULED: 'scheduled',
  LIVE: 'live',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  RESCHEDULED: 'rescheduled'
};

/**
 * Notification Types
 */
const NOTIFICATION_TYPES = {
  COURSE_ENROLLMENT: 'course_enrollment',
  COURSE_COMPLETION: 'course_completion',
  BATCH_REMINDER: 'batch_reminder',
  PAYMENT_SUCCESS: 'payment_success',
  PAYMENT_FAILED: 'payment_failed',
  ASSIGNMENT_DUE: 'assignment_due',
  CERTIFICATE_ISSUED: 'certificate_issued',
  SYSTEM_ANNOUNCEMENT: 'system_announcement',
  INSTRUCTOR_MESSAGE: 'instructor_message',
  COURSE_UPDATE: 'course_update'
};

/**
 * Notification Channels
 */
const NOTIFICATION_CHANNELS = {
  IN_APP: 'in_app',
  EMAIL: 'email',
  SMS: 'sms',
  PUSH: 'push'
};

/**
 * File Types
 */
const FILE_TYPES = {
  IMAGE: 'image',
  VIDEO: 'video',
  AUDIO: 'audio',
  DOCUMENT: 'document',
  ARCHIVE: 'archive',
  OTHER: 'other'
};

/**
 * Allowed File Extensions
 */
const ALLOWED_FILE_EXTENSIONS = {
  IMAGES: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
  VIDEOS: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'],
  AUDIO: ['.mp3', '.wav', '.ogg', '.m4a', '.aac'],
  DOCUMENTS: ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'],
  PRESENTATIONS: ['.ppt', '.pptx', '.odp'],
  SPREADSHEETS: ['.xls', '.xlsx', '.ods', '.csv'],
  ARCHIVES: ['.zip', '.rar', '.7z', '.tar', '.gz']
};

/**
 * File Size Limits (in bytes)
 */
const FILE_SIZE_LIMITS = {
  AVATAR: 5 * 1024 * 1024, // 5MB
  COURSE_THUMBNAIL: 10 * 1024 * 1024, // 10MB
  VIDEO: 500 * 1024 * 1024, // 500MB
  DOCUMENT: 50 * 1024 * 1024, // 50MB
  GENERAL: 100 * 1024 * 1024 // 100MB
};

/**
 * API Response Status
 */
const API_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

/**
 * HTTP Status Codes
 */
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
};

/**
 * Pagination Defaults
 */
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
  MIN_LIMIT: 1
};

/**
 * Search and Filter Defaults
 */
const SEARCH_DEFAULTS = {
  MIN_SEARCH_LENGTH: 2,
  MAX_SEARCH_LENGTH: 100,
  DEBOUNCE_DELAY: 300 // milliseconds
};

/**
 * Date and Time Formats
 */
const DATE_FORMATS = {
  API: 'YYYY-MM-DD',
  DISPLAY: 'DD/MM/YYYY',
  DATETIME: 'DD/MM/YYYY HH:mm',
  TIME: 'HH:mm',
  MONTH_YEAR: 'MMM YYYY',
  FULL_DATE: 'dddd, MMMM Do YYYY'
};

/**
 * Validation Limits
 */
const VALIDATION_LIMITS = {
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  EMAIL_MAX_LENGTH: 254,
  PHONE_LENGTH: 10,
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_MAX_LENGTH: 128,
  COURSE_TITLE_MIN_LENGTH: 5,
  COURSE_TITLE_MAX_LENGTH: 200,
  COURSE_DESCRIPTION_MIN_LENGTH: 20,
  COURSE_DESCRIPTION_MAX_LENGTH: 5000,
  REVIEW_MIN_LENGTH: 10,
  REVIEW_MAX_LENGTH: 1000,
  BIO_MAX_LENGTH: 500,
  ADDRESS_MAX_LENGTH: 200
};

/**
 * Rating Scale
 */
const RATING_SCALE = {
  MIN: 1,
  MAX: 5,
  DEFAULT: 0
};

/**
 * Progress Tracking
 */
const PROGRESS = {
  NOT_STARTED: 0,
  IN_PROGRESS: 1,
  COMPLETED: 100
};

/**
 * Time Zones (Common Indian Time Zones)
 */
const TIME_ZONES = {
  IST: 'Asia/Kolkata',
  UTC: 'UTC'
};

/**
 * Languages
 */
const LANGUAGES = {
  ENGLISH: 'english',
  HINDI: 'hindi',
  BENGALI: 'bengali',
  TAMIL: 'tamil',
  TELUGU: 'telugu',
  MARATHI: 'marathi',
  GUJARATI: 'gujarati',
  KANNADA: 'kannada',
  MALAYALAM: 'malayalam',
  PUNJABI: 'punjabi'
};

/**
 * Gender Options
 */
const GENDER_OPTIONS = {
  MALE: 'male',
  FEMALE: 'female',
  OTHER: 'other',
  PREFER_NOT_TO_SAY: 'prefer_not_to_say'
};

/**
 * Education Levels
 */
const EDUCATION_LEVELS = {
  HIGH_SCHOOL: 'High School',
  DIPLOMA: 'Diploma',
  BACHELORS: 'Bachelor\'s Degree',
  MASTERS: 'Master\'s Degree',
  PHD: 'PhD',
  PROFESSIONAL: 'Professional Certification'
};

/**
 * Days of Week
 */
const DAYS_OF_WEEK = {
  MONDAY: 'monday',
  TUESDAY: 'tuesday',
  WEDNESDAY: 'wednesday',
  THURSDAY: 'thursday',
  FRIDAY: 'friday',
  SATURDAY: 'saturday',
  SUNDAY: 'sunday'
};

/**
 * Error Messages
 */
const ERROR_MESSAGES = {
  REQUIRED_FIELD: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  INVALID_PHONE: 'Please enter a valid phone number',
  WEAK_PASSWORD: 'Password must be at least 8 characters with uppercase, lowercase, and number',
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
  INVALID_FILE_TYPE: 'Invalid file type',
  FILE_TOO_LARGE: 'File size exceeds limit',
  NETWORK_ERROR: 'Network error. Please try again.',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  NOT_FOUND: 'Resource not found',
  SERVER_ERROR: 'Internal server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again'
};

/**
 * Success Messages
 */
const SUCCESS_MESSAGES = {
  REGISTRATION_SUCCESS: 'Registration successful! Please check your email for verification.',
  LOGIN_SUCCESS: 'Login successful!',
  LOGOUT_SUCCESS: 'Logout successful!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  PASSWORD_CHANGED: 'Password changed successfully!',
  EMAIL_SENT: 'Email sent successfully!',
  COURSE_ENROLLED: 'Successfully enrolled in course!',
  PAYMENT_SUCCESS: 'Payment completed successfully!',
  FILE_UPLOADED: 'File uploaded successfully!',
  DATA_SAVED: 'Data saved successfully!'
};

// Export for both CommonJS (Node.js) and ES modules (frontend)
const constants = {
  USER_ROLES,
  USER_STATUS,
  COURSE_LEVELS,
  COURSE_STATUS,
  COURSE_CATEGORIES,
  PAYMENT_STATUS,
  PAYMENT_GATEWAYS,
  CURRENCIES,
  BATCH_STATUS,
  SESSION_TYPES,
  SESSION_STATUS,
  NOTIFICATION_TYPES,
  NOTIFICATION_CHANNELS,
  FILE_TYPES,
  ALLOWED_FILE_EXTENSIONS,
  FILE_SIZE_LIMITS,
  API_STATUS,
  HTTP_STATUS,
  PAGINATION,
  SEARCH_DEFAULTS,
  DATE_FORMATS,
  VALIDATION_LIMITS,
  RATING_SCALE,
  PROGRESS,
  TIME_ZONES,
  LANGUAGES,
  GENDER_OPTIONS,
  EDUCATION_LEVELS,
  DAYS_OF_WEEK,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
};

// CommonJS export (Node.js)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = constants;
}

// ES module export (frontend)
if (typeof window !== 'undefined') {
  window.Constants = constants;
}
