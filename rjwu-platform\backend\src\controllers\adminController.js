const UserService = require('../services/UserService');
const CourseService = require('../services/CourseService');
const PaymentService = require('../services/PaymentService');
const BatchService = require('../services/BatchService');
const AnalyticsService = require('../services/AnalyticsService');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

// Get dashboard analytics
const getDashboardAnalytics = async (req, res, next) => {
  try {
    const { period = '30' } = req.query; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    const filters = {
      startDate: startDate.toISOString(),
      endDate: new Date().toISOString()
    };

    // Get comprehensive analytics from AnalyticsService
    const [
      platformOverview,
      userAnalytics,
      courseAnalytics,
      paymentAnalytics,
      realTimeDashboard
    ] = await Promise.all([
      AnalyticsService.getPlatformOverview(filters),
      AnalyticsService.getUserAnalytics(filters),
      AnalyticsService.getCourseAnalytics(filters),
      AnalyticsService.getPaymentAnalytics(filters),
      AnalyticsService.getRealTimeDashboard()
    ]);

    // Get top performing courses using CourseService
    const topCoursesResult = await CourseService.getCourses(
      { status: 'published' },
      { limit: 10, sort: 'enrollmentCount', order: 'desc' }
    );

    logger.info('Admin dashboard analytics retrieved successfully', {
      adminId: req.user.id,
      period,
      timestamp: new Date().toISOString()
    });

    res.status(200).json({
      status: 'success',
      data: {
        overview: platformOverview.overview,
        growth: platformOverview.growth,
        distribution: platformOverview.distribution,
        users: {
          ...userAnalytics.engagementMetrics,
          registrationTrend: userAnalytics.registrationTrend
        },
        courses: {
          ...courseAnalytics.courseMetrics,
          categoryDistribution: courseAnalytics.categoryDistribution,
          creationTrend: courseAnalytics.creationTrend
        },
        payments: {
          ...paymentAnalytics.metrics,
          revenueTrend: paymentAnalytics.revenueTrend,
          gatewayDistribution: paymentAnalytics.gatewayDistribution
        },
        realTime: realTimeDashboard,
        topCourses: topCoursesResult.courses,
        topRevenueCourses: paymentAnalytics.topRevenueCourses
      }
    });
  } catch (error) {
    logger.error('Get dashboard analytics error:', error);
    next(error);
  }
};

// Get user management data
const getUserManagement = async (req, res, next) => {
  try {
    // Build filters from query parameters
    const filters = {};
    if (req.query.role) filters.role = req.query.role;
    if (req.query.isActive !== undefined) filters.isActive = req.query.isActive === 'true';
    if (req.query.isEmailVerified !== undefined) filters.isEmailVerified = req.query.isEmailVerified === 'true';

    // Date range filter
    if (req.query.startDate || req.query.endDate) {
      filters.createdAt = {};
      if (req.query.startDate) filters.createdAt.$gte = new Date(req.query.startDate);
      if (req.query.endDate) filters.createdAt.$lte = new Date(req.query.endDate);
    }

    // Options for pagination and sorting
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 20,
      sort: 'createdAt',
      order: 'desc',
      search: req.query.search
    };

    // Get users using UserService
    const result = await UserService.getUsers(filters, options);

    // Get user analytics for statistics
    const userAnalytics = await AnalyticsService.getUserAnalytics({
      startDate: req.query.startDate,
      endDate: req.query.endDate
    });

    logger.info('User management data retrieved successfully', {
      adminId: req.user.id,
      filters,
      page: options.page,
      limit: options.limit,
      timestamp: new Date().toISOString()
    });

    res.status(200).json({
      status: 'success',
      results: result.users.length,
      pagination: result.pagination,
      stats: {
        totalUsers: result.pagination.total,
        engagementMetrics: userAnalytics.engagementMetrics
      },
      data: {
        users: result.users
      }
    });
  } catch (error) {
    logger.error('Get user management error:', error);
    next(error);
  }
};

// Get course management data
const getCourseManagement = async (req, res, next) => {
  try {
    // Build filters from query parameters
    const filters = {};
    if (req.query.category) filters.category = req.query.category;
    if (req.query.level) filters.level = req.query.level;
    if (req.query.isActive !== undefined) filters.isActive = req.query.isActive === 'true';
    if (req.query.instructor) filters.instructor = req.query.instructor;

    // Options for pagination and sorting
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 20,
      sort: req.query.sortBy ? req.query.sortBy.split(':')[0] : 'createdAt',
      order: req.query.sortBy ? req.query.sortBy.split(':')[1] : 'desc',
      search: req.query.search
    };

    // Get courses using CourseService
    const result = await CourseService.getCourses(filters, options);

    // Get course analytics for statistics
    const courseAnalytics = await AnalyticsService.getCourseAnalytics({
      startDate: req.query.startDate,
      endDate: req.query.endDate,
      category: req.query.category,
      instructorId: req.query.instructor
    });

    logger.info('Course management data retrieved successfully', {
      adminId: req.user.id,
      filters,
      page: options.page,
      limit: options.limit,
      timestamp: new Date().toISOString()
    });

    res.status(200).json({
      status: 'success',
      results: result.courses.length,
      pagination: result.pagination,
      stats: {
        totalCourses: result.pagination.total,
        courseMetrics: courseAnalytics.courseMetrics,
        categoryDistribution: courseAnalytics.categoryDistribution,
        topCourses: courseAnalytics.topCourses
      },
      data: {
        courses: result.courses
      }
    });
  } catch (error) {
    logger.error('Get course management error:', error);
    next(error);
  }
};

// Get payment analytics
const getPaymentAnalytics = async (req, res, next) => {
  try {
    const { period = '30', gateway } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));

    const filters = {
      startDate: startDate.toISOString(),
      endDate: new Date().toISOString(),
      gateway
    };

    // Get comprehensive payment analytics using PaymentService
    const paymentAnalytics = await AnalyticsService.getPaymentAnalytics(filters);

    logger.info('Payment analytics retrieved successfully', {
      adminId: req.user.id,
      period,
      gateway,
      timestamp: new Date().toISOString()
    });

    res.status(200).json({
      status: 'success',
      data: {
        metrics: paymentAnalytics.metrics,
        revenue: {
          total: paymentAnalytics.metrics.totalRevenue,
          net: paymentAnalytics.metrics.netRevenue,
          period: paymentAnalytics.metrics.totalRevenue,
          trend: paymentAnalytics.revenueTrend
        },
        payments: {
          byGateway: paymentAnalytics.gatewayDistribution,
          transactions: paymentAnalytics.metrics.totalTransactions,
          averageOrderValue: paymentAnalytics.metrics.averageOrderValue
        },
        topCourses: paymentAnalytics.topRevenueCourses,
        refunds: {
          total: paymentAnalytics.metrics.totalRefunds,
          rate: paymentAnalytics.metrics.totalRefunds / paymentAnalytics.metrics.totalRevenue * 100
        }
      }
    });
  } catch (error) {
    logger.error('Get payment analytics error:', error);
    next(error);
  }
};

// Content moderation - Get flagged content
const getContentModeration = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    // Get courses with low ratings using CourseService
    const flaggedCoursesResult = await CourseService.getCourses(
      {
        $or: [
          { rating: { $lt: 2.5, $gt: 0 } },
          { 'reviews.rating': { $lt: 2 } }
        ]
      },
      { page, limit, sort: 'rating', order: 'asc' }
    );

    // Get users with suspicious activity using UserService
    const suspiciousUsersResult = await UserService.getUsers(
      {
        $or: [
          { loginAttempts: { $gte: 3 } },
          { isLocked: true }
        ]
      },
      { page: 1, limit: 20, sort: 'loginAttempts', order: 'desc' }
    );

    logger.info('Content moderation data retrieved successfully', {
      adminId: req.user.id,
      flaggedCoursesCount: flaggedCoursesResult.courses.length,
      suspiciousUsersCount: suspiciousUsersResult.users.length,
      timestamp: new Date().toISOString()
    });

    res.status(200).json({
      status: 'success',
      data: {
        flaggedCourses: flaggedCoursesResult.courses,
        suspiciousUsers: suspiciousUsersResult.users,
        pagination: {
          courses: flaggedCoursesResult.pagination,
          users: suspiciousUsersResult.pagination
        }
      }
    });
  } catch (error) {
    logger.error('Get content moderation error:', error);
    next(error);
  }
};

// System health check
const getSystemHealth = async (req, res, next) => {
  try {
    // Get platform overview for database health
    const platformOverview = await AnalyticsService.getPlatformOverview();

    // Get real-time dashboard data
    const realTimeData = await AnalyticsService.getRealTimeDashboard();

    // Check for system issues using services
    const issues = [];

    // Check for inactive courses with enrollments using CourseService
    const inactiveCoursesResult = await CourseService.getCourses(
      { isActive: false, enrollmentCount: { $gt: 0 } },
      { limit: 1 }
    );

    if (inactiveCoursesResult.pagination.total > 0) {
      issues.push(`${inactiveCoursesResult.pagination.total} inactive courses have active enrollments`);
    }

    // Check for old pending payments using PaymentService
    const oldPendingPayments = await PaymentService.getPaymentHistory(null, {
      status: 'pending',
      startDate: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
      endDate: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
      limit: 1
    });

    if (oldPendingPayments.pagination.total > 0) {
      issues.push(`${oldPendingPayments.pagination.total} payments pending for over 1 hour`);
    }

    // Check for locked users using UserService
    const lockedUsersResult = await UserService.getUsers(
      { isLocked: true },
      { limit: 1 }
    );

    if (lockedUsersResult.pagination.total > 0) {
      issues.push(`${lockedUsersResult.pagination.total} users are currently locked`);
    }

    logger.info('System health check performed successfully', {
      adminId: req.user.id,
      issuesCount: issues.length,
      timestamp: new Date().toISOString()
    });

    res.status(200).json({
      status: 'success',
      data: {
        database: {
          users: platformOverview.overview.totalUsers,
          courses: platformOverview.overview.totalCourses,
          payments: platformOverview.overview.totalPayments,
          batches: platformOverview.overview.totalBatches
        },
        realTime: realTimeData,
        issues,
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          timestamp: new Date()
        }
      }
    });
  } catch (error) {
    logger.error('Get system health error:', error);
    next(error);
  }
};

// Bulk user operations
const bulkUserOperations = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed: ' + errors.array().map(e => e.msg).join(', '), 400));
    }

    const { operation, userIds, data } = req.body;

    if (!Array.isArray(userIds) || userIds.length === 0) {
      return next(new AppError('User IDs array is required', 400));
    }

    let affectedCount = 0;
    const results = [];

    // Process each user individually using UserService for better error handling
    for (const userId of userIds) {
      try {
        let updateData = {};

        switch (operation) {
          case 'activate':
            updateData = { isActive: true };
            break;
          case 'deactivate':
            updateData = { isActive: false };
            break;
          case 'verify_email':
            updateData = { isEmailVerified: true };
            break;
          case 'change_role':
            if (!data?.role) {
              return next(new AppError('Role is required for role change operation', 400));
            }
            updateData = { role: data.role };
            break;
          default:
            return next(new AppError('Invalid operation', 400));
        }

        const updatedUser = await UserService.updateUser(userId, updateData);
        results.push({ userId, status: 'success', user: updatedUser });
        affectedCount++;
      } catch (error) {
        results.push({ userId, status: 'error', error: error.message });
        logger.warn(`Failed to update user ${userId} in bulk operation:`, error);
      }
    }

    logger.info(`Bulk user operation performed: ${operation} on ${userIds.length} users by ${req.user.email}`, {
      adminId: req.user.id,
      operation,
      totalUsers: userIds.length,
      successfulUpdates: affectedCount,
      failedUpdates: userIds.length - affectedCount,
      timestamp: new Date().toISOString()
    });

    res.status(200).json({
      status: 'success',
      message: `${operation} operation completed successfully`,
      data: {
        affectedUsers: affectedCount,
        totalUsers: userIds.length,
        operation,
        results: results
      }
    });
  } catch (error) {
    logger.error('Bulk user operations error:', error);
    next(error);
  }
};

module.exports = {
  getDashboardAnalytics,
  getUserManagement,
  getCourseManagement,
  getPaymentAnalytics,
  getContentModeration,
  getSystemHealth,
  bulkUserOperations
};
