import { NextApiRequest, NextApiResponse } from 'next'

interface HealthResponse {
  status: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: string
  uptime: number
  environment: string
  version: string
  services: {
    backend?: {
      status: 'healthy' | 'unhealthy'
      responseTime?: number
      error?: string
    }
  }
  memory: {
    rss: string
    heapTotal: string
    heapUsed: string
    external: string
  }
}

/**
 * Health check endpoint for the frontend application
 * Checks the health of the frontend and its dependencies
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<HealthResponse>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      services: {},
      memory: getMemoryUsage()
    })
  }

  try {
    const health: HealthResponse = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      services: {},
      memory: getMemoryUsage()
    }

    // Check backend API health
    try {
      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api'
      const healthUrl = backendUrl.replace('/api', '/health')
      
      const startTime = Date.now()
      const response = await fetch(healthUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Set a timeout for the request
        signal: AbortSignal.timeout(5000) // 5 second timeout
      })
      
      const responseTime = Date.now() - startTime
      
      if (response.ok) {
        health.services.backend = {
          status: 'healthy',
          responseTime
        }
      } else {
        health.services.backend = {
          status: 'unhealthy',
          responseTime,
          error: `HTTP ${response.status}: ${response.statusText}`
        }
        health.status = 'degraded'
      }
    } catch (error) {
      health.services.backend = {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
      health.status = 'degraded'
    }

    // Determine overall health status
    const unhealthyServices = Object.values(health.services).filter(
      service => service.status === 'unhealthy'
    )

    if (unhealthyServices.length > 0) {
      health.status = 'degraded'
      return res.status(503).json(health)
    }

    res.status(200).json(health)
  } catch (error) {
    const errorResponse: HealthResponse = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      services: {},
      memory: getMemoryUsage()
    }

    res.status(500).json(errorResponse)
  }
}

/**
 * Get memory usage information
 */
function getMemoryUsage() {
  const memUsage = process.memoryUsage()
  return {
    rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
    heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
    heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
    external: `${Math.round(memUsage.external / 1024 / 1024)}MB`
  }
}
