#!/usr/bin/env node

/**
 * RJWU Platform Startup Test Script
 * Tests basic functionality without requiring external dependencies
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 RJWU Platform Startup Test');
console.log('================================\n');

// Test 1: Check if all essential files exist
console.log('📁 Checking file structure...');

const essentialFiles = [
  // Backend files
  'backend/package.json',
  'backend/src/app.js',
  'backend/src/config/config.js',
  'backend/src/config/database.js',
  'backend/.env.example',
  
  // Frontend files
  'frontend/package.json',
  'frontend/pages/_app.tsx',
  'frontend/pages/index.tsx',
  'frontend/next.config.js',
  'frontend/.env.local.example',
  
  // Shared files
  'shared/utils/validation.js',
  'shared/constants/index.js',
  
  // Docker files
  'docker-compose.yml',
  'backend/Dockerfile',
  'frontend/Dockerfile',
  
  // Documentation
  'README.md',
  'API_DOCUMENTATION.md',
  'USER_GUIDE.md'
];

let missingFiles = [];

essentialFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.log(`\n⚠️  Found ${missingFiles.length} missing files:`);
  missingFiles.forEach(file => console.log(`   - ${file}`));
} else {
  console.log('\n✅ All essential files present!');
}

// Test 2: Check package.json files
console.log('\n📦 Checking package.json files...');

try {
  const backendPkg = JSON.parse(fs.readFileSync(path.join(__dirname, 'backend/package.json'), 'utf8'));
  console.log(`✅ Backend package.json - ${backendPkg.name}@${backendPkg.version}`);
  
  const frontendPkg = JSON.parse(fs.readFileSync(path.join(__dirname, 'frontend/package.json'), 'utf8'));
  console.log(`✅ Frontend package.json - ${frontendPkg.name}@${frontendPkg.version}`);
} catch (error) {
  console.log(`❌ Error reading package.json files: ${error.message}`);
}

// Test 3: Check environment files
console.log('\n🔧 Checking environment configuration...');

const envFiles = [
  'backend/.env.example',
  'frontend/.env.local.example'
];

envFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    console.log(`✅ ${file} - ${lines.length} environment variables`);
  } else {
    console.log(`❌ ${file} - MISSING`);
  }
});

// Test 4: Check shared utilities
console.log('\n🔗 Checking shared utilities...');

try {
  // Test validation utility
  const validationPath = path.join(__dirname, 'shared/utils/validation.js');
  if (fs.existsSync(validationPath)) {
    const validation = require(validationPath);
    const testEmail = validation.isValidEmail('<EMAIL>');
    console.log(`✅ Validation utility - Email test: ${testEmail}`);
  }
  
  // Test constants
  const constantsPath = path.join(__dirname, 'shared/constants/index.js');
  if (fs.existsSync(constantsPath)) {
    const constants = require(constantsPath);
    console.log(`✅ Constants utility - USER_ROLES: ${Object.keys(constants.USER_ROLES).length} roles`);
  }
} catch (error) {
  console.log(`❌ Error testing shared utilities: ${error.message}`);
}

// Test 5: Check Docker configuration
console.log('\n🐳 Checking Docker configuration...');

const dockerFiles = [
  'docker-compose.yml',
  'docker-compose.prod.yml',
  'backend/Dockerfile',
  'frontend/Dockerfile'
];

dockerFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
  }
});

// Test 6: Basic syntax check
console.log('\n🔍 Performing basic syntax checks...');

const jsFiles = [
  'backend/src/app.js',
  'backend/src/config/config.js',
  'shared/utils/validation.js'
];

jsFiles.forEach(file => {
  try {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      require(filePath);
      console.log(`✅ ${file} - Syntax OK`);
    }
  } catch (error) {
    console.log(`❌ ${file} - Syntax Error: ${error.message}`);
  }
});

// Summary
console.log('\n📊 Test Summary');
console.log('================');

if (missingFiles.length === 0) {
  console.log('✅ All essential files are present');
  console.log('✅ Project structure is complete');
  console.log('✅ Ready for development setup');
  
  console.log('\n🚀 Next Steps:');
  console.log('1. Install dependencies:');
  console.log('   cd backend && npm install');
  console.log('   cd frontend && npm install');
  console.log('');
  console.log('2. Set up environment files:');
  console.log('   cp backend/.env.example backend/.env');
  console.log('   cp frontend/.env.local.example frontend/.env.local');
  console.log('');
  console.log('3. Start MongoDB (if not using Docker):');
  console.log('   mongod');
  console.log('');
  console.log('4. Start the development servers:');
  console.log('   # Terminal 1 - Backend');
  console.log('   cd backend && npm run dev');
  console.log('   # Terminal 2 - Frontend');
  console.log('   cd frontend && npm run dev');
  console.log('');
  console.log('5. Or use Docker:');
  console.log('   docker-compose up -d');
  
} else {
  console.log(`❌ Found ${missingFiles.length} missing files`);
  console.log('❌ Please ensure all files are created before starting');
}

console.log('\n🎉 RJWU Platform validation complete!');
