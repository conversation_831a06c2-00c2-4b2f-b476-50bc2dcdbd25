import React from 'react'
import Head from 'next/head'
import Link from 'next/link'
import DashboardLayout from '@/components/layouts/DashboardLayout'
import { 
  BookOpenIcon, 
  ClockIcon, 
  TrophyIcon, 
  ChartBarIcon,
  PlayIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'

// Mock data - in real app, this would come from API
const mockData = {
  user: {
    name: '<PERSON>',
    role: 'student',
    avatar: null,
    enrolledCourses: 3,
    completedCourses: 1,
    totalStudyTime: 45,
    currentStreak: 7
  },
  enrolledCourses: [
    {
      id: 1,
      title: 'React.js Fundamentals',
      instructor: 'Dr. <PERSON>',
      progress: 75,
      nextLesson: 'State Management with Hooks',
      thumbnail: 'https://via.placeholder.com/300x200',
      totalLessons: 20,
      completedLessons: 15
    },
    {
      id: 2,
      title: 'Node.js Backend Development',
      instructor: 'Prof. <PERSON>',
      progress: 45,
      nextLesson: 'Express.js Middleware',
      thumbnail: 'https://via.placeholder.com/300x200',
      totalLessons: 25,
      completedLessons: 11
    },
    {
      id: 3,
      title: 'Database Design with MongoDB',
      instructor: 'Dr. <PERSON>',
      progress: 20,
      nextLesson: 'Schema Design Patterns',
      thumbnail: 'https://via.placeholder.com/300x200',
      totalLessons: 18,
      completedLessons: 4
    }
  ],
  upcomingClasses: [
    {
      id: 1,
      title: 'React.js Live Session',
      instructor: 'Dr. Sarah Wilson',
      date: '2024-01-15',
      time: '10:00 AM',
      duration: '1.5 hours',
      type: 'live'
    },
    {
      id: 2,
      title: 'Node.js Q&A Session',
      instructor: 'Prof. Michael Chen',
      date: '2024-01-16',
      time: '2:00 PM',
      duration: '1 hour',
      type: 'qa'
    }
  ],
  recentActivity: [
    {
      id: 1,
      type: 'lesson_completed',
      title: 'Completed "Introduction to Hooks"',
      course: 'React.js Fundamentals',
      timestamp: '2 hours ago'
    },
    {
      id: 2,
      type: 'quiz_passed',
      title: 'Passed React Components Quiz',
      course: 'React.js Fundamentals',
      score: 85,
      timestamp: '1 day ago'
    },
    {
      id: 3,
      type: 'course_enrolled',
      title: 'Enrolled in Database Design with MongoDB',
      timestamp: '3 days ago'
    }
  ]
}

export default function Dashboard() {
  const { user, enrolledCourses, upcomingClasses, recentActivity } = mockData

  return (
    <>
      <Head>
        <title>Dashboard - RJWU Platform</title>
        <meta name="description" content="Student dashboard for RJWU educational platform" />
      </Head>

      <DashboardLayout userRole="student">
        <div className="space-y-6">
          {/* Welcome Section */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg shadow-lg p-6 text-white">
            <h1 className="text-2xl font-bold mb-2">Welcome back, {user.name}!</h1>
            <p className="text-blue-100">Continue your learning journey and achieve your goals.</p>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <BookOpenIcon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Enrolled Courses</p>
                  <p className="text-2xl font-semibold text-gray-900">{user.enrolledCourses}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <TrophyIcon className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-semibold text-gray-900">{user.completedCourses}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <ClockIcon className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Study Time</p>
                  <p className="text-2xl font-semibold text-gray-900">{user.totalStudyTime}h</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <ChartBarIcon className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Current Streak</p>
                  <p className="text-2xl font-semibold text-gray-900">{user.currentStreak} days</p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Enrolled Courses */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Continue Learning</h2>
                </div>
                <div className="p-6 space-y-4">
                  {enrolledCourses.map((course) => (
                    <div key={course.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start space-x-4">
                        <img
                          src={course.thumbnail}
                          alt={course.title}
                          className="w-16 h-16 rounded-lg object-cover"
                        />
                        <div className="flex-1 min-w-0">
                          <h3 className="text-sm font-medium text-gray-900 truncate">
                            {course.title}
                          </h3>
                          <p className="text-sm text-gray-500">by {course.instructor}</p>
                          <div className="mt-2">
                            <div className="flex items-center justify-between text-sm text-gray-600">
                              <span>{course.completedLessons}/{course.totalLessons} lessons</span>
                              <span>{course.progress}% complete</span>
                            </div>
                            <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${course.progress}%` }}
                              />
                            </div>
                          </div>
                          <div className="mt-2 flex items-center justify-between">
                            <span className="text-sm text-gray-600">
                              Next: {course.nextLesson}
                            </span>
                            <Link
                              href={`/courses/${course.id}/continue`}
                              className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                            >
                              <PlayIcon className="h-4 w-4 mr-1" />
                              Continue
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Upcoming Classes */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Upcoming Classes</h2>
                </div>
                <div className="p-6 space-y-4">
                  {upcomingClasses.map((class_) => (
                    <div key={class_.id} className="border-l-4 border-blue-500 pl-4">
                      <h3 className="text-sm font-medium text-gray-900">{class_.title}</h3>
                      <p className="text-sm text-gray-600">by {class_.instructor}</p>
                      <div className="mt-1 flex items-center text-sm text-gray-500">
                        <CalendarIcon className="h-4 w-4 mr-1" />
                        {class_.date} at {class_.time}
                      </div>
                      <p className="text-xs text-gray-500">{class_.duration}</p>
                    </div>
                  ))}
                  <Link
                    href="/schedule"
                    className="block text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    View full schedule →
                  </Link>
                </div>
              </div>

              {/* Recent Activity */}
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-900">Recent Activity</h2>
                </div>
                <div className="p-6 space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="text-sm">
                      <p className="text-gray-900">{activity.title}</p>
                      {activity.course && (
                        <p className="text-gray-600">{activity.course}</p>
                      )}
                      {activity.score && (
                        <p className="text-green-600">Score: {activity.score}%</p>
                      )}
                      <p className="text-gray-500 text-xs">{activity.timestamp}</p>
                    </div>
                  ))}
                  <Link
                    href="/activity"
                    className="block text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    View all activity →
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </>
  )
}
