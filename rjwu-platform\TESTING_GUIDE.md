# Testing Guide

## Overview

This document provides comprehensive information about testing in the RJWU EduTech Platform. The platform uses Jest as the primary testing framework for both backend and frontend components.

## Test Structure

### Backend Tests
```
backend/
├── tests/
│   ├── setup.js              # Test configuration and utilities
│   ├── models/               # Model tests
│   │   ├── User.test.js
│   │   ├── Course.test.js
│   │   └── ...
│   ├── controllers/          # Controller tests
│   │   ├── authController.test.js
│   │   ├── courseController.test.js
│   │   └── ...
│   ├── services/             # Service tests
│   │   ├── EmailService.test.js
│   │   ├── PaymentService.test.js
│   │   └── ...
│   └── integration/          # Integration tests
├── jest.config.js            # Jest configuration
└── package.json              # Test scripts
```

### Frontend Tests
```
frontend/
├── tests/
│   ├── setup.js              # Test configuration and utilities
│   ├── components/           # Component tests
│   │   ├── ui/
│   │   │   ├── Button.test.tsx
│   │   │   └── ...
│   │   └── ...
│   ├── pages/                # Page tests
│   ├── hooks/                # Custom hook tests
│   └── utils/                # Utility function tests
├── jest.config.js            # Jest configuration
└── package.json              # Test scripts
```

## Running Tests

### Backend Tests

```bash
cd backend

# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test suites
npm run test:models
npm run test:controllers
npm run test:services

# Run tests for CI/CD
npm run test:ci
```

### Frontend Tests

```bash
cd frontend

# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test suites
npm run test:components
npm run test:pages
npm run test:hooks

# Run tests for CI/CD
npm run test:ci
```

## Test Configuration

### Backend Configuration (jest.config.js)

- **Test Environment**: Node.js
- **Test Database**: MongoDB Memory Server (in-memory)
- **Coverage Threshold**: 70% for all metrics
- **Setup File**: `tests/setup.js`

### Frontend Configuration (jest.config.js)

- **Test Environment**: jsdom (browser-like environment)
- **Framework Integration**: Next.js Jest configuration
- **Coverage Threshold**: 60% for all metrics
- **Setup File**: `tests/setup.js`

## Writing Tests

### Backend Test Examples

#### Model Tests
```javascript
describe('User Model', () => {
  test('should create a valid user', async () => {
    const userData = global.testUtils.createTestUser();
    const user = new User(userData);
    const savedUser = await user.save();

    expect(savedUser._id).toBeDefined();
    expect(savedUser.email).toBe(userData.email);
  });
});
```

#### Controller Tests
```javascript
describe('Auth Controller', () => {
  test('should register a new user', async () => {
    const userData = global.testUtils.createTestUser();

    const response = await request(app)
      .post('/api/auth/register')
      .send(userData)
      .expect(201);

    expect(response.body.status).toBe('success');
  });
});
```

#### Service Tests
```javascript
describe('EmailService', () => {
  test('should send email successfully', async () => {
    const result = await EmailService.sendEmail({
      to: '<EMAIL>',
      subject: 'Test',
      html: '<h1>Test</h1>'
    });

    expect(result.success).toBe(true);
  });
});
```

### Frontend Test Examples

#### Component Tests
```typescript
describe('Button Component', () => {
  test('renders button with text', () => {
    render(<Button>Click me</Button>);
    
    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
  });

  test('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

## Test Utilities

### Backend Utilities (global.testUtils)

- `createTestUser(overrides)` - Create test user data
- `createTestCourse(overrides)` - Create test course data
- `createTestBatch(overrides)` - Create test batch data
- `generateTestToken(userId)` - Generate JWT token for testing
- `wait(ms)` - Wait for specified time

### Frontend Utilities (global.testUtils)

- `createMockUser(overrides)` - Create mock user data
- `createMockCourse(overrides)` - Create mock course data
- `createMockApiResponse(data, status)` - Create mock API response
- `mockFetchResponse(data, status)` - Mock fetch response
- `mockFetchError(error)` - Mock fetch error
- `waitFor(ms)` - Wait for async operations

## Test Database

### Backend
- Uses MongoDB Memory Server for isolated testing
- Database is created fresh for each test run
- Collections are cleared after each test
- No external database dependencies

### Frontend
- Uses mocked API responses
- No real API calls during testing
- LocalStorage and SessionStorage are mocked
- Router and Next.js components are mocked

## Coverage Reports

### Viewing Coverage
```bash
# Backend
cd backend && npm run test:coverage
open coverage/lcov-report/index.html

# Frontend
cd frontend && npm run test:coverage
open coverage/lcov-report/index.html
```

### Coverage Thresholds

#### Backend
- Branches: 70%
- Functions: 70%
- Lines: 70%
- Statements: 70%

#### Frontend
- Branches: 60%
- Functions: 60%
- Lines: 60%
- Statements: 60%

## Best Practices

### General
1. **Test Structure**: Follow AAA pattern (Arrange, Act, Assert)
2. **Test Names**: Use descriptive test names that explain what is being tested
3. **Test Isolation**: Each test should be independent and not rely on other tests
4. **Mock External Dependencies**: Mock APIs, databases, and external services
5. **Test Edge Cases**: Include tests for error conditions and edge cases

### Backend Specific
1. **Database Cleanup**: Always clean up test data after each test
2. **Environment Variables**: Use test-specific environment variables
3. **Authentication**: Test both authenticated and unauthenticated scenarios
4. **Validation**: Test input validation and error handling
5. **Business Logic**: Focus on testing business logic in services

### Frontend Specific
1. **User Interactions**: Test user interactions like clicks, form submissions
2. **Accessibility**: Test ARIA attributes and keyboard navigation
3. **Responsive Design**: Test component behavior at different screen sizes
4. **Error States**: Test loading states, error states, and empty states
5. **Props Validation**: Test component behavior with different props

## Continuous Integration

### GitHub Actions
Tests are automatically run on:
- Pull requests
- Pushes to main branch
- Scheduled runs (daily)

### Test Commands for CI
```bash
# Backend
npm run test:ci

# Frontend
npm run test:ci
```

## Debugging Tests

### Backend
```bash
# Run specific test file
npm test -- tests/models/User.test.js

# Run tests with verbose output
npm test -- --verbose

# Run tests in debug mode
node --inspect-brk node_modules/.bin/jest --runInBand
```

### Frontend
```bash
# Run specific test file
npm test -- tests/components/ui/Button.test.tsx

# Run tests with verbose output
npm test -- --verbose

# Debug tests in VS Code
# Use "Debug Jest Tests" configuration
```

## Common Issues and Solutions

### Backend
1. **Database Connection**: Ensure MongoDB Memory Server is properly configured
2. **Async Operations**: Use proper async/await or return promises
3. **Environment Variables**: Check test environment variables are set
4. **Timeouts**: Increase timeout for slow operations

### Frontend
1. **Component Rendering**: Ensure proper imports and component structure
2. **Async Operations**: Use `waitFor` for async state updates
3. **Mocking**: Properly mock external dependencies
4. **DOM Queries**: Use appropriate queries from Testing Library

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Testing Library Documentation](https://testing-library.com/docs/)
- [Supertest Documentation](https://github.com/visionmedia/supertest)
- [MongoDB Memory Server](https://github.com/nodkz/mongodb-memory-server)
